#!/usr/bin/env python3
import sqlite3
import os

db_path = 'data/db/salary_system.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f"数据库文件: {db_path}")
    print(f"数据库大小: {os.path.getsize(db_path) / 1024 / 1024:.2f} MB")
    print(f"表的数量: {len(tables)}")
    print("\n表详情:")
    
    for table in tables:
        table_name = table[0]
        try:
            cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
            count = cursor.fetchone()[0]
            print(f"  - {table_name}: {count} 条记录")
        except Exception as e:
            print(f"  - {table_name}: 查询失败 - {e}")
    
    # 检查table_metadata表
    try:
        cursor.execute("SELECT table_name, table_type FROM table_metadata WHERE table_type='salary_data'")
        salary_tables = cursor.fetchall()
        print(f"\ntable_metadata中的工资数据表: {len(salary_tables)} 个")
        for table_name, table_type in salary_tables:
            print(f"  - {table_name} ({table_type})")
    except Exception as e:
        print(f"\n检查table_metadata失败: {e}")
    
    conn.close()
else:
    print(f"数据库文件不存在: {db_path}")
