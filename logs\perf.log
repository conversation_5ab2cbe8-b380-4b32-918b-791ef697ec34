2025-08-15 11:40:03.795 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-15 11:40:03.795 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-15 11:40:03.795 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-15 11:40:03.795 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-15 11:40:03.795 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-15 11:40:03.795 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-15 11:40:06.799 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-15 11:40:06.799 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-15 11:40:06.799 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-15 11:40:06.799 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-15 11:40:06.799 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-15 11:40:06.799 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-15 11:40:06.799 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 11:40:06.799 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-15 11:40:06.799 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-15 11:40:06.799 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-15 11:40:06.799 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-15 11:40:06.878 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-15 11:40:06.878 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-15 11:40:06.878 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 11:40:06.878 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 11:40:06.893 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-15 11:40:06.893 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-15 11:40:06.893 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-15 11:40:06.893 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 11:40:06.893 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-15 11:40:06.893 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-15 11:40:06.893 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-15 11:40:06.893 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-15 11:40:06.893 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11659 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-15 11:40:06.893 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 11:40:06.893 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11514 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-15 11:40:06.893 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11552 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-15 11:40:06.987 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-15 11:40:06.988 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-15 11:40:06.991 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 11:40:06.997 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:149 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-15 11:40:06.998 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-15 11:40:06.999 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-15 11:40:07.007 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 11:40:07.007 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-15 11:40:07.007 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-15 11:40:07.007 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-15 11:40:07.007 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 11:40:07.007 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-15 11:40:07.007 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 15.7ms
2025-08-15 11:40:07.023 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-15 11:40:07.023 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.prototype_main_window:__init__:3613 | 🚀 性能管理器已集成
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.prototype_main_window:__init__:3615 | ✅ 新架构集成成功！
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3728 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3693 | ✅ 新架构事件监听器设置完成
2025-08-15 11:40:07.038 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-15 11:40:07.038 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-15 11:40:07.282 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2689 | 菜单栏创建完成
2025-08-15 11:40:07.282 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-15 11:40:07.282 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-15 11:40:07.282 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-15 11:40:07.282 | INFO     | src.gui.prototype.prototype_main_window:__init__:2664 | 菜单栏管理器初始化完成
2025-08-15 11:40:07.282 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-15 11:40:07.282 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5583 | 管理器设置完成，包含增强版表头管理器
2025-08-15 11:40:07.298 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5588 | 🔧 开始应用窗口级Material Design样式...
2025-08-15 11:40:07.298 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-15 11:40:07.298 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-15 11:40:07.298 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5595 | ✅ 窗口级样式应用成功
2025-08-15 11:40:07.298 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5636 | ✅ 响应式样式监听设置完成
2025-08-15 11:40:07.298 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-15 11:40:07.298 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-15 11:40:07.298 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-15 11:40:07.298 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-15 11:40:07.314 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-15 11:40:07.330 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:07.330 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1503 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-15 11:40:07.330 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1783 | 使用兜底数据加载导航
2025-08-15 11:40:07.330 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-15 11:40:07.345 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1566 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-15 11:40:07.345 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 11:40:07.345 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-15 11:40:07.345 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-15 11:40:07.356 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-15 11:40:07.356 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-15 11:40:07.360 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-15 11:40:07.379 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:07.380 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1503 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-15 11:40:07.383 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-15 11:40:07.387 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1566 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-15 11:40:07.388 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 11:40:07.390 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-15 11:40:07.392 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1353 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-15 11:40:07.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1660 | 开始获取最新工资数据路径...
2025-08-15 11:40:07.396 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:07.403 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1665 | 未找到任何工资数据表
2025-08-15 11:40:07.403 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1550 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-15 11:40:07.404 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1358 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 11:40:07.671 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-15 11:40:07.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2138 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-15 11:40:07.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1365 | 快捷键注册完成: 18/18 个
2025-08-15 11:40:07.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1805 | 拖拽排序管理器初始化完成
2025-08-15 11:40:07.700 | INFO     | src.modules.data_management.data_flow_validator:__init__:78 | 🔧 [数据验证器] 初始化完成，验证级别: moderate
2025-08-15 11:40:07.701 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-15 11:40:07.706 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-15 11:40:07.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2173 | 🔧 [排序修复] 数据流验证器和状态管理器初始化成功
2025-08-15 11:40:07.708 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-15 11:40:07.708 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-15 11:40:07.709 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 11:40:07.711 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2225 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-15 11:40:07.726 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-15 11:40:07.728 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-15 11:40:07.729 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2272 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-15 11:40:07.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1552 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-15 11:40:07.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-15 11:40:07.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-15 11:40:07.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-15 11:40:07.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-15 11:40:07.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2279 | 列宽管理器初始化完成
2025-08-15 11:40:07.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2406 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-15 11:40:07.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2293 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-15 11:40:07.748 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:07.748 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:07.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:07.752 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:07.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:07.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:07.776 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:07.777 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:07.779 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:07.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:07.781 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:40:07.788 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-15 11:40:07.790 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 35.3ms
2025-08-15 11:40:07.792 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:07.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:07.794 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-15 11:40:07.795 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:07.796 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:07.811 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-15 11:40:07.821 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-15 11:40:07.828 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-15 11:40:07.874 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-15 11:40:07.922 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 11:40:07.923 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-15 11:40:07.926 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5545 | 快捷键设置完成
2025-08-15 11:40:07.927 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5502 | 主窗口UI设置完成。
2025-08-15 11:40:07.928 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5739 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-15 11:40:07.929 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5771 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-15 11:40:07.930 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5783 | ✅ 已连接分页刷新信号到主窗口
2025-08-15 11:40:07.933 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5784 | ✅ 已连接分页组件事件到新架构
2025-08-15 11:40:07.935 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5795 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-15 11:40:07.940 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5798 | 信号连接设置完成
2025-08-15 11:40:07.941 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6904 | 已加载字段映射信息，共0个表的映射
2025-08-15 11:40:07.955 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:07.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:07.964 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:07.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:07.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:07.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:07.975 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:07.986 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:07.988 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:07.988 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:07.990 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 24.5ms
2025-08-15 11:40:07.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:07.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:07.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-15 11:40:07.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:08.007 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:08.017 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:40:08.020 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8393 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-15 11:40:08.022 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:08.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:08.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:08.031 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:08.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:08.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:08.038 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:08.056 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:08.059 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:08.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:08.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 30.3ms
2025-08-15 11:40:08.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:08.063 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:08.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-15 11:40:08.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:08.077 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:08.079 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 22
2025-08-15 11:40:08.080 | INFO     | src.gui.prototype.prototype_main_window:__init__:3667 | 原型主窗口初始化完成
2025-08-15 11:40:08.151 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-15 11:40:08.176 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:08.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:08.180 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1423 | 执行延迟的自动选择最新数据...
2025-08-15 11:40:08.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:08.289 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1660 | 开始获取最新工资数据路径...
2025-08-15 11:40:08.294 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:08.295 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1665 | 未找到任何工资数据表
2025-08-15 11:40:08.296 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1358 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 11:40:08.298 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1446 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-15 11:40:08.343 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-15 11:40:08.344 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 11:40:08.408 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1660 | 开始获取最新工资数据路径...
2025-08-15 11:40:08.410 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:08.415 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1665 | 未找到任何工资数据表
2025-08-15 11:40:08.416 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1358 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 11:40:08.856 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9295 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-15 11:40:08.857 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9205 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-15 11:40:08.860 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9219 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-15 11:40:08.861 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9753 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-15 11:40:08.877 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9225 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-15 11:40:09.297 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1660 | 开始获取最新工资数据路径...
2025-08-15 11:40:09.298 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:09.301 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1665 | 未找到任何工资数据表
2025-08-15 11:40:09.302 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1561 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-15 11:40:09.303 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1358 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 11:40:16.639 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-15 11:40:16.639 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-08-15 11:40:16.642 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-08-15 11:40:16.643 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:40:16.644 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '离休人员'] -> salary_data_2025_05_retired_employees
2025-08-15 11:40:16.645 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:40:16.646 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_retired_employees 的缓存
2025-08-15 11:40:16.649 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 2 个表格到表头管理器
2025-08-15 11:40:16.649 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-15 11:40:16.650 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.61ms
2025-08-15 11:40:16.655 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 11:40:16.656 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_retired_employees（通过事件系统）
2025-08-15 11:40:16.656 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 11:40:16.657 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:40:16.658 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 3 个总表 (尝试 1/5)
2025-08-15 11:40:16.660 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_retired_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:40:16.660 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8316 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-15 11:40:16.661 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-15 11:40:16.664 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-15 11:40:16.669 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:16.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:16.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:16.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:16.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:16.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:16.677 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:16.679 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:16.687 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:16.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:16.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:40:16.689 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 18.3ms
2025-08-15 11:40:16.690 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:16.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:16.697 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:40:16.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:16.700 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:16.701 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:40:16.702 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 15
2025-08-15 11:40:16.799 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:18.143 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-15 11:40:18.143 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-08-15 11:40:18.143 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-15 11:40:18.143 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:40:18.143 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '退休人员'] -> salary_data_2025_05_pension_employees
2025-08-15 11:40:18.143 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:40:18.143 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_pension_employees 的缓存
2025-08-15 11:40:18.143 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 2 个表格到表头管理器
2025-08-15 11:40:18.143 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-15 11:40:18.143 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-15 11:40:18.143 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 11:40:18.143 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_pension_employees（通过事件系统）
2025-08-15 11:40:18.143 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 11:40:18.143 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:40:18.158 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 3 个总表 (尝试 1/5)
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_pension_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8328 | 🔧 [数据流追踪] 使用退休人员表头: 26个字段
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_pension_employees 的专用表头: 26个字段
2025-08-15 11:40:18.158 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:18.158 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:18.174 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:18.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:18.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:40:18.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 30.6ms
2025-08-15 11:40:18.191 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:18.191 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:18.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:40:18.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:18.201 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:18.202 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:40:18.204 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 26
2025-08-15 11:40:18.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:18.987 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表', '工资表 > 2025年']
2025-08-15 11:40:18.987 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-15 11:40:18.987 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension_employees -> None
2025-08-15 11:40:18.987 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:40:18.987 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-15 11:40:18.987 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:40:18.987 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-15 11:40:18.987 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 2 个表格到表头管理器
2025-08-15 11:40:18.987 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-15 11:40:18.987 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:40:19.003 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 3 个总表 (尝试 1/5)
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8339 | 🔧 [数据流追踪] 使用A岗职工表头: 20个字段
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-15 11:40:19.003 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:19.003 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:19.020 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:19.020 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:19.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 50.8ms
2025-08-15 11:40:19.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:19.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:19.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:40:19.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:19.067 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:19.069 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 20
2025-08-15 11:40:19.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:23.370 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 11:40:23.386 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1353 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-15 11:40:23.386 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1660 | 开始获取最新工资数据路径...
2025-08-15 11:40:23.386 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:23.386 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1665 | 未找到任何工资数据表
2025-08-15 11:40:23.386 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1550 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-15 11:40:23.395 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1358 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 11:40:24.387 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1660 | 开始获取最新工资数据路径...
2025-08-15 11:40:24.387 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:24.387 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1665 | 未找到任何工资数据表
2025-08-15 11:40:24.392 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1358 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 11:40:24.664 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-15 11:40:24.665 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 异动人员表
2025-08-15 11:40:24.668 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-15 11:40:24.669 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:40:24.670 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8099 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-08-15 11:40:24.670 | WARNING  | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8103 | 🔧 [表名生成] 异动表路径不完整(1层): ['异动人员表']
2025-08-15 11:40:24.672 | INFO     | src.gui.prototype.prototype_main_window:set_data:798 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-15 11:40:24.672 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:24.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:24.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:24.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:24.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:24.676 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:24.684 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:24.685 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:24.687 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:24.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:24.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:40:24.690 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 15.4ms
2025-08-15 11:40:24.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:24.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:24.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:40:24.703 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:24.704 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:24.705 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:40:24.705 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8339 | 🔧 [数据流追踪] 使用A岗职工表头: 20个字段
2025-08-15 11:40:24.706 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-15 11:40:24.717 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:40:24.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:40:24.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:40:24.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:40:24.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:40:24.733 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:40:24.736 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:40:24.737 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:40:24.738 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:40:24.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:40:24.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 20.2ms
2025-08-15 11:40:24.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:40:24.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:40:24.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:40:24.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:40:24.752 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:40:24.758 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 20
2025-08-15 11:40:24.827 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:24.852 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:40:35.263 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-15 11:40:35.263 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6031 | 接收到数据导入请求，推断的目标路径: 异动人员表。打开导入对话框。
2025-08-15 11:40:35.278 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-15 11:40:35.278 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-15 11:40:35.278 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-08-15 11:40:35.294 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:40:35.340 | INFO     | src.gui.main_dialogs:_get_template_fields:1880 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-15 11:40:35.340 | INFO     | src.gui.main_dialogs:_init_field_mapping:1867 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-15 11:40:35.403 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-15 11:40:35.403 | INFO     | src.gui.main_dialogs:_apply_default_settings:2231 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-15 11:40:35.403 | INFO     | src.gui.main_dialogs:_setup_tooltips:2486 | 工具提示设置完成
2025-08-15 11:40:35.403 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2525 | 快捷键设置完成
2025-08-15 11:40:35.403 | INFO     | src.gui.main_dialogs:__init__:78 | 数据导入对话框初始化完成。
2025-08-15 11:40:35.419 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:84 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-15 11:40:35.419 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6042 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-15 11:40:40.637 | INFO     | src.gui.main_dialogs:_on_target_changed:2157 | 目标位置已更新: 异动人员表 > 2025年 > 8月 > 全部在职人员
2025-08-15 11:40:43.935 | INFO     | src.gui.main_dialogs:_on_target_changed:2157 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 11:40:43.935 | INFO     | src.gui.main_dialogs:_on_target_changed:2176 | 注意：数据期间(2025-08)与目标位置月份(2025-12)不同
2025-08-15 11:40:54.997 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 11:40:56.658 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-15 11:40:56.658 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2266 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-15 11:41:31.185 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 11:41:31.372 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-15 11:41:31.372 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:216 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 11:41:31.575 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:233 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 11:41:31.575 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 11:41:31.575 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 11:41:31.685 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-15 11:41:31.685 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 11:41:31.685 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-15 11:41:31.685 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-15 11:41:31.700 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 11:41:31.700 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-15 11:41:31.700 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-15 11:41:31.700 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:499 | 工作表 离休人员工资表 使用智能默认配置
2025-08-15 11:41:31.700 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:787 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 11:41:31.700 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-15 11:41:31.700 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:516 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-15 11:41:31.716 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_retired_employees
2025-08-15 11:41:31.716 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_retired_employees
2025-08-15 11:41:31.716 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:563 | 为表 salary_data_2025_12_retired_employees 生成标准化字段映射: 21 个字段
2025-08-15 11:41:31.716 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:584 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-15 11:41:31.716 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:399 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-08-15 11:41:31.748 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:483 | 成功创建异动数据表: change_data_2025_12_retired_employees
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1759 | [FIX] [修复标识] 导入字段映射加载完成: 23 个映射规则
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1767 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1776 | [FIX] [修复标识] 导入列名映射成功: 18 个字段已映射
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 new_value 设置默认值
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 序号 设置默认值
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 备注 设置默认值
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 detected_at 设置默认值
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 field_name 设置默认值
2025-08-15 11:41:31.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-08-15 11:41:31.841 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 基本
离休费 设置默认值
2025-08-15 11:41:31.857 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_type 设置默认值
2025-08-15 11:41:31.857 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 住房
补贴 设置默认值
2025-08-15 11:41:31.857 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 护理费 设置默认值
2025-08-15 11:41:31.857 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 生活
补贴 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 结余
津贴 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 verified 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_amount 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 离休
补贴 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 补发 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_reason 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 合计 设置默认值
2025-08-15 11:41:31.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 借支 设置默认值
2025-08-15 11:41:31.919 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 增发一次
性生活补贴 设置默认值
2025-08-15 11:41:31.919 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 物业
补贴 设置默认值
2025-08-15 11:41:31.919 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-08-15 11:41:31.919 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 人员代码 设置默认值
2025-08-15 11:41:31.919 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 old_value 设置默认值
2025-08-15 11:41:31.935 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1913 | 成功向表 change_data_2025_12_retired_employees 保存 2 条数据。
2025-08-15 11:41:31.935 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 11:41:32.044 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:499 | 工作表 退休人员工资表 使用智能默认配置
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:787 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:516 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_pension_employees
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_pension_employees
2025-08-15 11:41:32.059 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:563 | 为表 salary_data_2025_12_pension_employees 生成标准化字段映射: 32 个字段
2025-08-15 11:41:32.075 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:584 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-15 11:41:32.075 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:399 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-08-15 11:41:32.075 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:483 | 成功创建异动数据表: change_data_2025_12_pension_employees
2025-08-15 11:41:32.091 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1759 | [FIX] [修复标识] 导入字段映射加载完成: 27 个映射规则
2025-08-15 11:41:32.106 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1767 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 11:41:32.106 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1776 | [FIX] [修复标识] 导入列名映射成功: 29 个字段已映射
2025-08-15 11:41:32.106 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 增资预付 设置默认值
2025-08-15 11:41:32.106 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 人员类别代码 设置默认值
2025-08-15 11:41:32.122 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 new_value 设置默认值
2025-08-15 11:41:32.122 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 序号 设置默认值
2025-08-15 11:41:32.122 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 备注 设置默认值
2025-08-15 11:41:32.142 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 detected_at 设置默认值
2025-08-15 11:41:32.142 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 field_name 设置默认值
2025-08-15 11:41:32.142 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 结余津贴 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2023待遇调整 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_type 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 护理费 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 基本退休费 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2021待遇调整 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 verified 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_amount 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2016待遇调整 设置默认值
2025-08-15 11:41:32.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 公积 设置默认值
2025-08-15 11:41:32.169 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2022待遇调整 设置默认值
2025-08-15 11:41:32.169 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 补发 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 离退休生活补贴 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_reason 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 借支 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2019待遇调整 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 保险扣款 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2017待遇调整 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 津贴 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 应发工资 设置默认值
2025-08-15 11:41:32.185 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 物业补贴 设置默认值
2025-08-15 11:41:32.200 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-08-15 11:41:32.200 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 住房补贴 设置默认值
2025-08-15 11:41:32.216 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2020待遇调整 设置默认值
2025-08-15 11:41:32.216 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2018待遇调整 设置默认值
2025-08-15 11:41:32.216 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 人员代码 设置默认值
2025-08-15 11:41:32.216 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 old_value 设置默认值
2025-08-15 11:41:32.232 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1913 | 成功向表 change_data_2025_12_pension_employees 保存 13 条数据。
2025-08-15 11:41:32.232 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 11:41:32.388 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 11:41:32.388 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-15 11:41:32.388 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:499 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:787 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:516 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_active_employees
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_active_employees
2025-08-15 11:41:32.403 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:563 | 为表 salary_data_2025_12_active_employees 生成标准化字段映射: 28 个字段
2025-08-15 11:41:32.419 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:584 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-15 11:41:32.419 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:399 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-08-15 11:41:32.435 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:483 | 成功创建异动数据表: change_data_2025_12_active_employees
2025-08-15 11:41:32.450 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1759 | [FIX] [修复标识] 导入字段映射加载完成: 23 个映射规则
2025-08-15 11:41:32.450 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1767 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 11:41:32.450 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1776 | [FIX] [修复标识] 导入列名映射成功: 25 个字段已映射
2025-08-15 11:41:32.466 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 人员类别代码 设置默认值
2025-08-15 11:41:32.466 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 new_value 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 工号 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 序号 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025公积金 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 车补 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 detected_at 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 代扣代存养老保险 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 field_name 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 结余津贴 设置默认值
2025-08-15 11:41:32.481 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-08-15 11:41:32.497 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_type 设置默认值
2025-08-15 11:41:32.497 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 人员类别 设置默认值
2025-08-15 11:41:32.497 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 verified 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_amount 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年薪级工资 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 交通补贴 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 卫生费 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 补发 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年岗位工资 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 通讯补贴 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_reason 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年奖励性绩效预发 设置默认值
2025-08-15 11:41:32.513 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 借支 设置默认值
2025-08-15 11:41:32.528 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 津贴 设置默认值
2025-08-15 11:41:32.528 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 应发工资 设置默认值
2025-08-15 11:41:32.545 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 物业补贴 设置默认值
2025-08-15 11:41:32.545 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-08-15 11:41:32.545 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 住房补贴 设置默认值
2025-08-15 11:41:32.545 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年基础性绩效 设置默认值
2025-08-15 11:41:32.545 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 old_value 设置默认值
2025-08-15 11:41:32.591 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1913 | 成功向表 change_data_2025_12_active_employees 保存 1396 条数据。
2025-08-15 11:41:32.591 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-15 11:41:32.591 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 11:41:32.591 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 11:41:32.700 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 11:41:32.700 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-15 11:41:32.700 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 63行 x 21列
2025-08-15 11:41:32.700 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 62行 × 21列
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:499 | 工作表 A岗职工 使用智能默认配置
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:787 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:516 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_a_grade_employees
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_a_grade_employees
2025-08-15 11:41:32.716 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:563 | 为表 salary_data_2025_12_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-15 11:41:32.732 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:584 | Sheet A岗职工 数据处理完成: 62 行
2025-08-15 11:41:32.747 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:399 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-08-15 11:41:32.747 | INFO     | src.modules.data_storage.dynamic_table_manager:create_change_data_table:483 | 成功创建异动数据表: change_data_2025_12_a_grade_employees
2025-08-15 11:41:32.762 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1759 | [FIX] [修复标识] 导入字段映射加载完成: 21 个映射规则
2025-08-15 11:41:32.762 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1767 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1776 | [FIX] [修复标识] 导入列名映射成功: 23 个字段已映射
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 人员类别代码 设置默认值
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 new_value 设置默认值
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 工号 设置默认值
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 序号 设置默认值
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025公积金 设置默认值
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 车补 设置默认值
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 detected_at 设置默认值
2025-08-15 11:41:32.778 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 代扣代存养老保险 设置默认值
2025-08-15 11:41:32.810 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年生活补贴 设置默认值
2025-08-15 11:41:32.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 field_name 设置默认值
2025-08-15 11:41:32.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 结余津贴 设置默认值
2025-08-15 11:41:32.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年校龄工资 设置默认值
2025-08-15 11:41:32.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 部门名称 设置默认值
2025-08-15 11:41:32.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_type 设置默认值
2025-08-15 11:41:32.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 人员类别 设置默认值
2025-08-15 11:41:32.825 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 verified 设置默认值
2025-08-15 11:41:32.841 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_amount 设置默认值
2025-08-15 11:41:32.841 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 卫生费 设置默认值
2025-08-15 11:41:32.841 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 补发 设置默认值
2025-08-15 11:41:32.841 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年岗位工资 设置默认值
2025-08-15 11:41:32.841 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 change_reason 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年奖励性绩效预发 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 借支 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 保险扣款 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 津贴 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 应发工资 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 姓名 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 2025年基础性绩效 设置默认值
2025-08-15 11:41:32.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1897 | [FIX] [修复标识] 字段 old_value 设置默认值
2025-08-15 11:41:32.888 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1913 | 成功向表 change_data_2025_12_a_grade_employees 保存 62 条数据。
2025-08-15 11:41:32.903 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:252 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_retired_employees 保存 2 条数据。', 'table_name': 'change_data_2025_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_pension_employees 保存 13 条数据。', 'table_name': 'change_data_2025_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_active_employees 保存 1396 条数据。', 'table_name': 'change_data_2025_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 change_data_2025_12_a_grade_employees 保存 62 条数据。', 'table_name': 'change_data_2025_12_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-15 11:41:32.903 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1487 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_retired_employees 保存 2 条数据。', 'table_name': 'change_data_2025_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_pension_employees 保存 13 条数据。', 'table_name': 'change_data_2025_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_active_employees 保存 1396 条数据。', 'table_name': 'change_data_2025_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 change_data_2025_12_a_grade_employees 保存 62 条数据。', 'table_name': 'change_data_2025_12_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-12', 'data_description': '2025年12月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '异动人员表 > 2025年 > 12月 > 全部在职人员'}
2025-08-15 11:41:32.903 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6061 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_retired_employees 保存 2 条数据。', 'table_name': 'change_data_2025_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_pension_employees 保存 13 条数据。', 'table_name': 'change_data_2025_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 change_data_2025_12_active_employees 保存 1396 条数据。', 'table_name': 'change_data_2025_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 change_data_2025_12_a_grade_employees 保存 62 条数据。', 'table_name': 'change_data_2025_12_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-12', 'data_description': '2025年12月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '异动人员表 > 2025年 > 12月 > 全部在职人员'}
2025-08-15 11:41:32.903 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6072 | 导入模式: multi_sheet, 目标路径: '异动人员表 > 2025年 > 12月 > 全部在职人员'
2025-08-15 11:41:32.903 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6090 | 接收到导入数据, 来源: 未知来源, 目标路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 11:41:32.919 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1798 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-15 11:41:32.935 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-15 11:41:32.935 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 异动人员表
2025-08-15 11:41:32.935 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-15 11:41:32.935 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:41:32.935 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8099 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-08-15 11:41:32.935 | WARNING  | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8103 | 🔧 [表名生成] 异动表路径不完整(1层): ['异动人员表']
2025-08-15 11:41:32.950 | INFO     | src.gui.prototype.prototype_main_window:set_data:798 | 空数据输入发生 2 次（2s 窗口），将显示空表提示
2025-08-15 11:41:32.966 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:41:32.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:41:32.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:41:32.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:41:32.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:41:32.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:41:32.966 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:41:32.966 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:41:32.982 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:41:33.028 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:41:33.028 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:41:33.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 81.5ms
2025-08-15 11:41:33.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:41:33.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:41:33.093 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:41:33.095 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:41:33.095 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:41:33.096 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:41:33.097 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8339 | 🔧 [数据流追踪] 使用A岗职工表头: 20个字段
2025-08-15 11:41:33.098 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-15 11:41:33.105 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:41:33.107 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:41:33.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:41:33.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:41:33.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:41:33.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:41:33.120 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:41:33.121 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:41:33.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:41:33.124 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:41:33.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 13.0ms
2025-08-15 11:41:33.129 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:41:33.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:41:33.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:41:33.138 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:41:33.145 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:41:33.147 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 20
2025-08-15 11:41:33.148 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 异动人员表
2025-08-15 11:41:33.165 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:41:33.166 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1503 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-15 11:41:33.177 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-15 11:41:33.177 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 11:41:33.178 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1893 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-15 11:41:33.180 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-15 11:41:33.185 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1924 | 🔧 [P2-2] 导航状态恢复完成: 1个展开项
2025-08-15 11:41:33.186 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1846 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-15 11:41:33.187 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 11:41:33.191 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1798 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-15 11:41:33.193 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表', '工资表 > 2025年']
2025-08-15 11:41:33.214 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-15 11:41:33.214 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-15 11:41:33.215 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:41:33.216 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-15 11:41:33.216 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:41:33.217 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-15 11:41:33.221 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 4 个表格到表头管理器
2025-08-15 11:41:33.226 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-15 11:41:33.231 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 4.66ms
2025-08-15 11:41:33.232 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 11:41:33.233 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-15 11:41:33.234 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 11:41:33.235 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:41:33.243 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 7 个总表 (尝试 1/5)
2025-08-15 11:41:33.244 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:41:33.248 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:41:33.253 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1503 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-15 11:41:33.260 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1783 | 使用兜底数据加载导航
2025-08-15 11:41:33.283 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-15 11:41:33.291 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月', '工资表 > 2025年', '工资表']
2025-08-15 11:41:33.295 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-08-15 11:41:33.296 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-15 11:41:33.297 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:41:33.298 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '全部在职人员'] -> salary_data_2025_05_active_employees
2025-08-15 11:41:33.298 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:41:33.300 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_active_employees 的缓存
2025-08-15 11:41:33.303 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 4 个表格到表头管理器
2025-08-15 11:41:33.308 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_active_employees（通过事件系统）
2025-08-15 11:41:33.309 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:41:33.312 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 7 个总表 (尝试 1/5)
2025-08-15 11:41:33.313 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_active_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:41:33.314 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 11:41:33.315 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1893 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-15 11:41:33.323 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月']
2025-08-15 11:41:33.335 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-15 11:41:33.335 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_active_employees -> None
2025-08-15 11:41:33.336 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:41:33.336 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-15 11:41:33.338 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:41:33.339 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-15 11:41:33.342 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 4 个表格到表头管理器
2025-08-15 11:41:33.347 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-15 11:41:33.351 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 7 个总表 (尝试 1/5)
2025-08-15 11:41:33.352 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:41:33.353 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1924 | 🔧 [P2-2] 导航状态恢复完成: 3个展开项
2025-08-15 11:41:33.354 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1846 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-15 11:41:33.355 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 11:41:33.361 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1353 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-15 11:41:33.362 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1660 | 开始获取最新工资数据路径...
2025-08-15 11:41:33.365 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:41:33.366 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1665 | 未找到任何工资数据表
2025-08-15 11:41:33.366 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1550 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-15 11:41:33.367 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1358 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 11:41:33.410 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_a_grade_employees
2025-08-15 11:41:33.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_05_a_grade_employees
2025-08-15 11:41:34.169 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6351 | 🔧 [修复] 强制导航到导入路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 11:41:34.170 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1798 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-15 11:41:34.174 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-15 11:41:34.174 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 异动人员表
2025-08-15 11:41:34.176 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-15 11:41:34.176 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:41:34.178 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8099 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-08-15 11:41:34.178 | WARNING  | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8103 | 🔧 [表名生成] 异动表路径不完整(1层): ['异动人员表']
2025-08-15 11:41:34.180 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:41:34.185 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:41:34.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:41:34.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:41:34.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:41:34.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:41:34.190 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:41:34.191 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:41:34.193 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:41:34.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:41:34.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:41:34.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 14.6ms
2025-08-15 11:41:34.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:41:34.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:41:34.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:41:34.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:41:34.212 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:41:34.218 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:41:34.219 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8339 | 🔧 [数据流追踪] 使用A岗职工表头: 20个字段
2025-08-15 11:41:34.221 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-15 11:41:34.222 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:41:34.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:41:34.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:41:34.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 11:41:34.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:41:34.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:41:34.242 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:41:34.243 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:41:34.244 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:41:34.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:41:34.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 21.3ms
2025-08-15 11:41:34.248 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:41:34.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:41:34.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:41:34.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:41:34.258 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:41:34.262 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 20
2025-08-15 11:41:34.273 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 异动人员表
2025-08-15 11:41:34.276 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 11:41:34.277 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1503 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-15 11:41:34.282 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1152 | 🔧 [P1修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-15 11:41:34.285 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 11:41:34.286 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1893 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-15 11:41:34.292 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-15 11:41:34.296 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1924 | 🔧 [P2-2] 导航状态恢复完成: 1个展开项
2025-08-15 11:41:34.301 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1846 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-15 11:41:34.302 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6356 | 导航面板数据已刷新
2025-08-15 11:41:34.303 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6400 | 已切换到异动表TAB
2025-08-15 11:41:34.304 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8099 | 🔧 [表名生成] 处理异动表路径: ['异动人员表', '2025年', '12月', '全部在职人员']
2025-08-15 11:41:34.305 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8126 | 🔧 [表名生成] 生成异动表名: change_data_2025_12_active_employees
2025-08-15 11:41:34.306 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6406 | 🔧 [修复] 更新当前表名: change_data_2025_12_active_employees
2025-08-15 11:41:34.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:41:34.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:41:34.809 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6522 | [数据流追踪] 开始智能数据显示刷新: change_data_2025_12_active_employees
2025-08-15 11:41:34.809 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6527 | 🔧 [P1修复] 刷新数据时更新表名: change_data_2025_12_active_employees
2025-08-15 11:41:34.818 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-08-15 11:41:34.819 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=1396, 页面大小=50, 用户偏好=None
2025-08-15 11:41:34.820 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms, 决策耗时=1ms
2025-08-15 11:41:34.824 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6549 | [数据流追踪] 智能分页策略决策: pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms
2025-08-15 11:41:34.825 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:6623 | [数据流追踪] 执行分页显示模式: change_data_2025_12_active_employees, 1396条记录
2025-08-15 11:41:34.826 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:7651 | 使用分页模式加载 change_data_2025_12_active_employees，第1页，每页50条
2025-08-15 11:41:34.827 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:7803 | 缓存未命中，从数据库加载: change_data_2025_12_active_employees 第1页
2025-08-15 11:41:34.829 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 开始加载表 change_data_2025_12_active_employees 第1页数据，每页50条
2025-08-15 11:41:34.830 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:6641 | [数据流追踪] 分页显示模式: 1396条记录分28页显示
2025-08-15 11:41:34.831 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:712 | 正在从表 change_data_2025_12_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-15 11:41:34.848 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:828 | 成功从表 change_data_2025_12_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-15 11:41:34.849 | INFO     | src.gui.prototype.prototype_main_window:run:182 | 🔧 [P2修复] 使用排序查询: 0 个排序列, 总记录数: 1396
2025-08-15 11:41:34.852 | INFO     | src.gui.prototype.prototype_main_window:run:234 | 原始数据: 50行, 37列
2025-08-15 11:41:34.857 | INFO     | src.gui.prototype.prototype_main_window:run:241 | 开始应用字段映射
2025-08-15 11:41:34.859 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:7048 | 开始统一字段处理: change_data_2025_12_active_employees, 原始列数: 37
2025-08-15 11:41:34.997 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-15 11:41:34.998 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 11:41:35.001 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:881 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-15 11:41:35.003 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-15 11:41:35.006 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:896 | 🎯 [统一状态管理] 状态同步完成
2025-08-15 11:41:35.007 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-15 11:41:35.009 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-15 11:41:35.014 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_07_active_employees 自动生成字段类型配置
2025-08-15 11:41:35.016 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_07_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-15 11:41:35.017 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_07_active_employees
2025-08-15 11:41:35.019 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_retired_employees 自动生成字段类型配置
2025-08-15 11:41:35.022 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-15 11:41:35.031 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_retired_employees
2025-08-15 11:41:35.031 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_pension_employees 自动生成字段类型配置
2025-08-15 11:41:35.033 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-15 11:41:35.034 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_pension_employees
2025-08-15 11:41:35.034 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_active_employees 自动生成字段类型配置
2025-08-15 11:41:35.035 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-15 11:41:35.036 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_active_employees
2025-08-15 11:41:35.038 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_a_grade_employees 自动生成字段类型配置
2025-08-15 11:41:35.039 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-15 11:41:35.046 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_a_grade_employees
2025-08-15 11:41:35.050 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_12_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_12_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 11个表的existing_display_fields为空: salary_data_2025_07_active_employees, active_employees, salary_data_2025_12_retired_employees 等11个表']
2025-08-15 11:41:35.051 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 11:41:35.053 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_12_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-15 11:41:35.054 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_12_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-15 11:41:35.055 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_12_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 11:41:35.072 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_12_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 11:41:35.073 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-15 11:41:35.074 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-15 11:41:35.076 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 part_time_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'hourly_rate', 'hours_worked']...
2025-08-15 11:41:35.077 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 contract_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'base_salary', 'performance_bonus']...
2025-08-15 11:41:35.078 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 11:41:35.080 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2065 | 🔧 [架构优化] 应用智能修复策略，执行 11 项修复操作
2025-08-15 11:41:35.101 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 11:41:35.105 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 5
2025-08-15 11:41:35.107 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 11:41:35.107 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-15 11:41:35.108 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-15 11:41:35.111 | INFO     | src.core.architecture_factory:get_unified_format_manager:293 | 🎯 [统一格式管理] 统一格式管理器创建成功
2025-08-15 11:41:35.120 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'updated_at', 'id', 'sequence_number', 'row_number']
2025-08-15 11:41:35.129 | INFO     | src.modules.format_management.format_renderer:render_dataframe:113 | 🎯 [格式渲染] 已隐藏字段: ['id']
2025-08-15 11:41:35.148 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: 补发 -> 类型: string
2025-08-15 11:41:35.153 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: 借支 -> 类型: string
2025-08-15 11:41:35.157 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 0个字段，原始字段数: 0
2025-08-15 11:41:35.157 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=change_data_2025_12_active_employees, display_fields=0个字段
2025-08-15 11:41:35.160 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 0个字段，原始字段数: 0
2025-08-15 11:41:35.165 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 0个字段，原始字段数: 0
2025-08-15 11:41:35.166 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 0个字段，原始字段数: 0
2025-08-15 11:41:35.168 | WARNING  | src.modules.format_management.format_renderer:render_dataframe:227 | 🔧 [格式修复] display_fields为空，table_type=change_data_2025_12_active_employees，尝试获取默认配置
2025-08-15 11:41:35.171 | INFO     | src.modules.format_management.format_renderer:_get_default_display_fields:301 | 🔧 [异动表修复] 异动表默认配置匹配: 22/27 字段
2025-08-15 11:41:35.176 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🔧 [格式修复] 使用默认字段配置: 22个字段
2025-08-15 11:41:35.181 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: change_data_2025_12_active_employees, 行数: 50, 列数: 22
2025-08-15 11:41:35.182 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: change_data_2025_12_active_employees, 行数: 50, 列数: 22
2025-08-15 11:41:35.184 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:7056 | 🔧 [调试] 格式化时删除的列: {'change_type', 'year', 'new_value', '序号', 'data_source', 'change_reason', 'detected_at', 'verified', 'change_amount', 'field_name', 'month', 'old_value', 'import_time', 'id', 'employee_id'}
2025-08-15 11:41:35.188 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 change_data_2025_12_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 11:41:35.196 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:7109 | 🔧 [字段处理] 统一字段处理完成并缓存: 22个字段
2025-08-15 11:41:35.197 | INFO     | src.gui.prototype.prototype_main_window:run:251 | PaginationWorker - 字段映射成功: 37 -> 22列
2025-08-15 11:41:35.198 | INFO     | src.gui.prototype.prototype_main_window:run:265 | 字段映射成功: 22列
2025-08-15 11:41:35.201 | INFO     | src.gui.prototype.prototype_main_window:run:291 | 最终数据: 50行, 22列, 总记录数: 1396
2025-08-15 11:41:35.203 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7834 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-08-15 11:41:35.210 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7868 | 🚀 [性能缓存] 数据已缓存: change_data_2025_12_active_employees 第1页
2025-08-15 11:41:35.217 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:654 | 正在从表 change_data_2025_12_active_employees 分页获取数据: 第2页, 每页50条
2025-08-15 11:41:35.219 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7897 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 22列
2025-08-15 11:41:35.226 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:687 | 成功从表 change_data_2025_12_active_employees 获取第2页数据: 50 行，总计1396行
2025-08-15 11:41:35.240 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-15 11:41:35.247 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 change_data_2025_12_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 11:41:35.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: change_data_2025_12_active_employees
2025-08-15 11:41:35.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-08-15 11:41:35.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-08-15 11:41:35.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 1000 -> 50
2025-08-15 11:41:35.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-15 11:41:35.274 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 11:41:35.275 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-15 11:41:35.276 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 11:41:35.282 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-15 11:41:35.282 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 11:41:35.285 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:881 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-15 11:41:35.285 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:896 | 🎯 [统一状态管理] 状态同步完成
2025-08-15 11:41:35.288 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-15 11:41:35.293 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-15 11:41:35.301 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 11:41:35.301 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-15 11:41:35.302 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-15 11:41:35.303 | INFO     | src.modules.format_management.unified_format_manager:__init__:1079 | 🔧 [单例优化] 单例统一格式管理器初始化完成
2025-08-15 11:41:35.304 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence_number', 'updated_at', 'created_at', 'id', 'row_number']
2025-08-15 11:41:35.305 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 11:41:35.315 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 11:41:35.317 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 11:41:35.319 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 11:41:35.320 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 11:41:35.320 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/25 个字段
2025-08-15 11:41:35.339 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始22个 -> 最终22个字段
2025-08-15 11:41:35.340 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 22
2025-08-15 11:41:35.341 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 22
2025-08-15 11:41:35.358 | INFO     | src.modules.format_management.format_config:load_config:403 | 🔧 [格式配置] 配置文件加载成功
2025-08-15 11:41:35.450 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-15 11:41:35.451 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-08-15 11:41:35.460 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时9.6ms, 平均每行0.19ms
2025-08-15 11:41:35.471 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-08-15 11:41:35.471 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=9.6ms, 策略=small_dataset
2025-08-15 11:41:35.474 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 11:41:35.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 未知0
2025-08-15 11:41:35.481 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 未知1
2025-08-15 11:41:35.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 未知2
2025-08-15 11:41:35.484 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 未知3
2025-08-15 11:41:35.485 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 未知4
2025-08-15 11:41:35.486 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-15 11:41:35.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=未知0, 薪资=0.00
2025-08-15 11:41:35.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=未知1, 薪资=0.00
2025-08-15 11:41:35.488 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=未知2, 薪资=0.00
2025-08-15 11:41:35.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=未知3, 薪资=0.00
2025-08-15 11:41:35.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=未知4, 薪资=0.00
2025-08-15 11:41:35.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['未知0', '未知1', '未知2', '未知3', '未知4']
2025-08-15 11:41:35.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 22 列
2025-08-15 11:41:35.500 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: change_data_2025_12_active_employees
2025-08-15 11:41:35.504 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-15 11:41:35.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 244.1ms
2025-08-15 11:41:35.512 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:41:35.513 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:41:35.515 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_active_employees 的列宽配置
2025-08-15 11:41:35.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:41:35.528 | INFO     | src.core.pagination_state_manager:__init__:63 | 🔧 [立即修复] 分页状态管理器初始化完成
2025-08-15 11:41:35.529 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-15 11:41:35.530 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7916 | 🔍 [调试-分页] 即将调用set_pagination_state: 第1页, 记录1-50
2025-08-15 11:41:35.530 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 11:41:35.531 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-15 11:41:35.531 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 11:41:35.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 22
2025-08-15 11:41:35.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-15 11:41:35.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 change_data_2025_12_active_employees 的列宽配置
2025-08-15 11:41:35.541 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: change_data_2025_12_active_employees
2025-08-15 11:41:35.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 11:41:35.543 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-08-15 11:41:35.544 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-15 11:41:35.546 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7918 | 🔍 [调试-分页] set_pagination_state调用完成
2025-08-15 11:41:35.547 | INFO     | src.gui.widgets.pagination_widget:set_total_records:442 | 📊[pagination-write] set_total_records | old_total=0 -> new_total=1396 | pages_old=1 -> pages_new=28
2025-08-15 11:41:35.549 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7940 | 🔧 [P0-新1修复] 分页状态验证: 当前第1页，共28页，总记录1396条
2025-08-15 11:41:35.632 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-15 11:41:35.633 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10878 | ✅ [新架构] 组件状态一致性验证通过
2025-08-15 11:41:35.635 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10841 | 🆕 [新架构] 导航迁移完成
2025-08-15 11:41:35.636 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7991 | 🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移
2025-08-15 11:41:35.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: change_data_2025_12_active_employees
2025-08-15 11:42:07.143 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 11:42:09.190 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月']
2025-08-15 11:42:09.190 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > 退休人员
2025-08-15 11:42:09.190 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_12_active_employees -> None
2025-08-15 11:42:09.206 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:42:09.206 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7174 | 🔧 [缓存清理] 清理了所有 1 个字段处理缓存条目
2025-08-15 11:42:09.206 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '退休人员'] -> salary_data_2025_05_pension_employees
2025-08-15 11:42:09.206 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:42:09.206 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_pension_employees 的缓存
2025-08-15 11:42:09.206 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 4 个表格到表头管理器
2025-08-15 11:42:09.206 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-15 11:42:09.206 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-15 11:42:09.206 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 11:42:09.206 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_pension_employees（通过事件系统）
2025-08-15 11:42:09.206 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 11:42:09.206 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:42:09.206 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 7 个总表 (尝试 1/5)
2025-08-15 11:42:09.206 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_pension_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8328 | 🔧 [数据流追踪] 使用退休人员表头: 26个字段
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_pension_employees 的专用表头: 26个字段
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 4 次（2s 窗口），将显示空表提示
2025-08-15 11:42:09.221 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:42:09.221 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:42:09.238 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:42:09.238 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:42:09.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:42:09.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:42:09.253 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-15 11:42:09.255 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-15 11:42:09.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 31.5ms
2025-08-15 11:42:09.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:42:09.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:42:09.267 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:09.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:42:09.268 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:42:09.271 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:42:09.285 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 26
2025-08-15 11:42:09.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:09.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: default_table
2025-08-15 11:42:09.327 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-15 11:42:09.367 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月']
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > A岗职工
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_pension_employees -> None
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7174 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', 'A岗职工'] -> salary_data_2025_05_a_grade_employees
2025-08-15 11:42:10.165 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:42:10.165 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_a_grade_employees 的缓存
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 4 个表格到表头管理器
2025-08-15 11:42:10.165 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_a_grade_employees（通过事件系统）
2025-08-15 11:42:10.180 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:42:10.180 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 7 个总表 (尝试 1/5)
2025-08-15 11:42:10.180 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_a_grade_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:42:10.180 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8339 | 🔧 [数据流追踪] 使用A岗职工表头: 20个字段
2025-08-15 11:42:10.180 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_a_grade_employees 的专用表头: 20个字段
2025-08-15 11:42:10.180 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:42:10.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:42:10.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:42:10.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-15 11:42:10.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:42:10.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:42:10.196 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:42:10.196 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:42:10.196 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:42:10.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:42:10.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 25.7ms
2025-08-15 11:42:10.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:42:10.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:42:10.208 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:10.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:42:10.210 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:42:10.214 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 20
2025-08-15 11:42:10.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:10.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: default_table
2025-08-15 11:42:10.266 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-15 11:42:10.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:42:12.357 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月']
2025-08-15 11:42:12.357 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-08-15 11:42:12.357 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_a_grade_employees -> None
2025-08-15 11:42:12.357 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:42:12.357 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7174 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-15 11:42:12.357 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '全部在职人员'] -> salary_data_2025_05_active_employees
2025-08-15 11:42:12.357 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:42:12.357 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_active_employees 的缓存
2025-08-15 11:42:12.357 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 4 个表格到表头管理器
2025-08-15 11:42:12.357 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-15 11:42:12.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 15.69ms
2025-08-15 11:42:12.373 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_active_employees（通过事件系统）
2025-08-15 11:42:12.373 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 11:42:12.373 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:42:12.373 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 7 个总表 (尝试 1/5)
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_active_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8306 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_active_employees 的专用表头: 22个字段
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 2 次（2s 窗口），将显示空表提示
2025-08-15 11:42:12.373 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-15 11:42:12.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:42:12.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:42:12.391 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:42:12.391 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:42:12.391 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:42:12.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:42:12.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 11:42:12.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 35.6ms
2025-08-15 11:42:12.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:42:12.410 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:42:12.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:12.419 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:42:12.419 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:42:12.420 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:42:12.421 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 22
2025-08-15 11:42:12.477 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:12.478 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: default_table
2025-08-15 11:42:12.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-15 11:42:12.553 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:42:13.131 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月']
2025-08-15 11:42:13.131 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-08-15 11:42:13.131 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_active_employees -> None
2025-08-15 11:42:13.131 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 11:42:13.131 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7174 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-15 11:42:13.131 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '离休人员'] -> salary_data_2025_05_retired_employees
2025-08-15 11:42:13.131 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 11:42:13.131 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_retired_employees 的缓存
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 4 个表格到表头管理器
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_05_retired_employees（通过事件系统）
2025-08-15 11:42:13.147 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 11:42:13.147 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1171 | 🔧 [P1修复] 找到 7 个总表 (尝试 1/5)
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7524 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_retired_employees 尚未创建，显示空表格等待数据导入
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8316 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8389 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-15 11:42:13.147 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 11:42:13.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 11:42:13.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 27.6ms
2025-08-15 11:42:13.175 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 11:42:13.176 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 11:42:13.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:13.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 11:42:13.180 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 11:42:13.183 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8411 | 已显示标准空表格，表头数量: 15
2025-08-15 11:42:13.188 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表 > 2025年 > 05月 > 离休人员
2025-08-15 11:42:13.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 11:42:13.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: default_table
2025-08-15 11:42:13.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-15 11:42:13.278 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 11:42:15.497 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 11:42:21.982 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-15 11:42:22.013 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_2184689817872 已自动清理（弱引用回调）
