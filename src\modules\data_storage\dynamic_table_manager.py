"""
月度工资异动处理系统 - 动态表管理器

本模块实现动态表管理功能：
- 工资数据表的动态创建
- 表结构的版本管理
- 字段映射和验证
- 表结构变更处理
- 索引自动管理

主要功能：
- create_salary_table(): 创建工资数据表
- update_table_schema(): 更新表结构
- manage_table_indexes(): 管理表索引
- validate_table_structure(): 验证表结构
- get_table_columns(): 获取表字段信息

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
作者: 开发团队
"""

import json
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from datetime import datetime
from dataclasses import dataclass
import pandas as pd

# 导入项目内部模块
from src.utils.log_config import setup_logger
from src.modules.system_config import ConfigManager
from src.modules.system_config.specialized_table_templates import SpecializedTableTemplates
from .database_manager import DatabaseManager, get_database_manager

# 初始化日志
logger = setup_logger("data_storage.dynamic_table_manager")


@dataclass
class ColumnDefinition:
    """列定义信息"""
    name: str
    type: str  # TEXT, INTEGER, REAL, BLOB
    nullable: bool = True
    default: Optional[str] = None
    unique: bool = False
    description: Optional[str] = None


@dataclass
class TableSchema:
    """表结构定义"""
    table_name: str
    columns: List[ColumnDefinition]
    primary_key: List[str]
    indexes: List[Dict[str, Any]]
    version: int = 1
    description: Optional[str] = None


class DynamicTableManager:
    """动态表管理器，负责表结构的动态创建和管理"""
    
    def __init__(self, 
                 db_manager: Optional[DatabaseManager] = None,
                 config_manager: Optional[ConfigManager] = None):
        """
        初始化动态表管理器
        
        Args:
            db_manager: 数据库管理器实例
            config_manager: 配置管理器实例
        """
        self.logger = setup_logger("data_storage.dynamic_table_manager")
        
        # 获取管理器实例
        self.db_manager = db_manager or get_database_manager()
        self.config_manager = config_manager or ConfigManager()
        self.specialized_templates = SpecializedTableTemplates()
        
        # 预定义的表结构模板
        self._table_templates = self._initialize_table_templates()
        
        # 支持的数据类型映射
        self._type_mapping = {
            'string': 'TEXT',
            'str': 'TEXT',
            'text': 'TEXT',
            'varchar': 'TEXT',
            'int': 'INTEGER',
            'integer': 'INTEGER',
            'number': 'INTEGER',
            'float': 'REAL',
            'real': 'REAL',
            'decimal': 'REAL',
            'bool': 'INTEGER',
            'boolean': 'INTEGER',
            'date': 'TEXT',
            'datetime': 'TEXT',
            'timestamp': 'TEXT'
        }

        # 🆕 [P1修复] 启动时数据库状态验证
        self._perform_startup_database_validation()

        self.logger.info("动态表管理器初始化完成")

    def _perform_startup_database_validation(self):
        """🆕 [P1修复] 执行启动时数据库状态验证"""
        try:
            self.logger.debug("🔧 [P1修复] 开始启动时数据库状态验证")

            # 1. 确保数据库完全就绪
            if not self._ensure_database_ready():
                self.logger.warning("🔧 [P1修复] 启动时数据库就绪检查失败")
                return

            # 2. 预热数据库连接和缓存
            self._warmup_database_connections()

            # 3. 验证关键表的可访问性
            self._validate_critical_tables()

            # 4. 检查并修复潜在的WAL状态问题
            self._check_and_fix_wal_state()

            self.logger.debug("🔧 [P1修复] 启动时数据库状态验证完成")

        except Exception as e:
            self.logger.warning(f"🔧 [P1修复] 启动时数据库状态验证失败: {e}")

    def _warmup_database_connections(self):
        """🆕 [P1修复] 预热数据库连接和缓存"""
        try:
            # 执行一些轻量级查询来预热连接
            self.db_manager.execute_query("SELECT 1")
            self.db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")

            # 预热表元数据查询
            self.db_manager.execute_query("SELECT COUNT(*) FROM table_metadata")

            self.logger.debug("🔧 [P1修复] 数据库连接预热完成")
        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] 数据库连接预热失败: {e}")

    def _validate_critical_tables(self):
        """🆕 [P1修复] 验证关键表的可访问性"""
        try:
            critical_tables = ['table_metadata', 'system_config']

            for table_name in critical_tables:
                # 检查表是否存在
                result = self.db_manager.execute_query(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    (table_name,)
                )
                if not result:
                    self.logger.warning(f"🔧 [P1修复] 关键表 {table_name} 不存在")
                    continue

                # 尝试访问表
                count_result = self.db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                if count_result:
                    count = count_result[0].get('count', 0)
                    self.logger.debug(f"🔧 [P1修复] 关键表 {table_name} 可访问，记录数: {count}")
                else:
                    self.logger.warning(f"🔧 [P1修复] 无法访问关键表 {table_name}")

        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] 关键表验证失败: {e}")

    def _check_and_fix_wal_state(self):
        """🆕 [P1修复] 检查并修复潜在的WAL状态问题"""
        try:
            # 检查WAL文件状态
            result = self.db_manager.execute_query("PRAGMA wal_checkpoint(PASSIVE)")
            if result and len(result) > 0:
                checkpoint_info = result[0]
                busy = checkpoint_info.get('busy', 0)
                log_pages = checkpoint_info.get('log', 0)

                self.logger.debug(f"🔧 [P1修复] WAL状态检查: busy={busy}, log_pages={log_pages}")

                # 如果有未提交的页面，执行完整checkpoint
                if log_pages > 0:
                    self.logger.debug("🔧 [P1修复] 检测到未提交的WAL页面，执行完整checkpoint")
                    self.db_manager.execute_query("PRAGMA wal_checkpoint(FULL)")

                    # 再次检查状态
                    result2 = self.db_manager.execute_query("PRAGMA wal_checkpoint(PASSIVE)")
                    if result2:
                        info2 = result2[0]
                        self.logger.debug(f"🔧 [P1修复] checkpoint后WAL状态: busy={info2.get('busy', 0)}, log_pages={info2.get('log', 0)}")

        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] WAL状态检查失败: {e}")

    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在

        Args:
            table_name (str): 要检查的表名

        Returns:
            bool: 如果表存在则返回 True，否则返回 False
        """
        try:
            return self.db_manager.table_exists(table_name)
        except Exception as e:
            self.logger.error(f"检查表 {table_name} 是否存在时出错: {e}")
            return False
    
    def _initialize_table_templates(self) -> Dict[str, TableSchema]:
        """初始化预定义的表结构模板"""
        templates = {}
        
        # 工资数据表模板
        salary_columns = [
            ColumnDefinition("id", "INTEGER", False, None, True, "自增主键"),
            ColumnDefinition("employee_id", "TEXT", False, None, False, "员工工号"),
            ColumnDefinition("employee_name", "TEXT", False, None, False, "员工姓名"),
            ColumnDefinition("id_card", "TEXT", True, None, False, "身份证号"),
            ColumnDefinition("department", "TEXT", True, None, False, "部门"),
            ColumnDefinition("position", "TEXT", True, None, False, "职位"),
            ColumnDefinition("basic_salary", "REAL", True, "0", False, "基本工资"),
            ColumnDefinition("performance_bonus", "REAL", True, "0", False, "绩效奖金"),
            ColumnDefinition("overtime_pay", "REAL", True, "0", False, "加班费"),
            ColumnDefinition("allowance", "REAL", True, "0", False, "津贴"),
            ColumnDefinition("deduction", "REAL", True, "0", False, "扣款"),
            ColumnDefinition("total_salary", "REAL", True, "0", False, "总工资"),
            ColumnDefinition("month", "TEXT", False, None, False, "月份"),
            ColumnDefinition("year", "INTEGER", False, None, False, "年份"),
            ColumnDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            ColumnDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
        
        templates["salary_data"] = TableSchema(
            table_name="salary_data",
            columns=salary_columns,
            primary_key=["id"],
            indexes=[
                {"name": "idx_employee_id", "columns": ["employee_id"], "unique": False},
                {"name": "idx_month_year", "columns": ["month", "year"], "unique": False},
                {"name": "idx_employee_month", "columns": ["employee_id", "month", "year"], "unique": True}
            ],
            description="工资数据表"
        )
        
        # 异动记录表模板
        change_columns = [
            ColumnDefinition("id", "INTEGER", False, None, True, "自增主键"),
            ColumnDefinition("employee_id", "TEXT", False, None, False, "员工工号"),
            ColumnDefinition("change_type", "TEXT", False, None, False, "异动类型"),
            ColumnDefinition("field_name", "TEXT", False, None, False, "变化字段"),
            ColumnDefinition("old_value", "TEXT", True, None, False, "旧值"),
            ColumnDefinition("new_value", "TEXT", True, None, False, "新值"),
            ColumnDefinition("change_amount", "REAL", True, "0", False, "变化金额"),
            ColumnDefinition("change_reason", "TEXT", True, None, False, "变化原因"),
            ColumnDefinition("month", "TEXT", False, None, False, "月份"),
            ColumnDefinition("year", "INTEGER", False, None, False, "年份"),
            ColumnDefinition("detected_at", "TEXT", False, None, False, "检测时间"),
            ColumnDefinition("verified", "INTEGER", False, "0", False, "是否验证")
        ]
        
        templates["salary_changes"] = TableSchema(
            table_name="salary_changes",
            columns=change_columns,
            primary_key=["id"],
            indexes=[
                {"name": "idx_change_employee", "columns": ["employee_id"], "unique": False},
                {"name": "idx_change_type", "columns": ["change_type"], "unique": False},
                {"name": "idx_change_month", "columns": ["month", "year"], "unique": False}
            ],
            description="工资异动记录表"
        )
        
        # 杂项数据表模板（用于未分类的工资数据）
        misc_data_columns = [
            ColumnDefinition("id", "INTEGER", False, None, True, "自增主键"),
            ColumnDefinition("employee_id", "TEXT", False, None, False, "员工工号"),
            ColumnDefinition("employee_name", "TEXT", False, None, False, "员工姓名"),
            ColumnDefinition("id_card", "TEXT", True, None, False, "身份证号"),
            ColumnDefinition("department", "TEXT", True, None, False, "部门"),
            ColumnDefinition("position", "TEXT", True, None, False, "职位"),
            ColumnDefinition("basic_salary", "REAL", True, "0", False, "基本工资"),
            ColumnDefinition("performance_bonus", "REAL", True, "0", False, "绩效奖金"),
            ColumnDefinition("overtime_pay", "REAL", True, "0", False, "加班费"),
            ColumnDefinition("allowance", "REAL", True, "0", False, "津贴"),
            ColumnDefinition("deduction", "REAL", True, "0", False, "扣款"),
            ColumnDefinition("total_salary", "REAL", True, "0", False, "总工资"),
            ColumnDefinition("data_source", "TEXT", True, None, False, "数据来源"),
            ColumnDefinition("import_time", "TEXT", True, None, False, "导入时间"),
            ColumnDefinition("month", "TEXT", False, None, False, "月份"),
            ColumnDefinition("year", "INTEGER", False, None, False, "年份"),
            ColumnDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            ColumnDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
        
        templates["misc_data"] = TableSchema(
            table_name="misc_data",
            columns=misc_data_columns,
            primary_key=["id"],
            indexes=[
                {"name": "idx_misc_employee_id", "columns": ["employee_id"], "unique": False},
                {"name": "idx_misc_month_year", "columns": ["month", "year"], "unique": False},
                {"name": "idx_misc_data_source", "columns": ["data_source"], "unique": False},
                {"name": "idx_misc_employee_month", "columns": ["employee_id", "month", "year"], "unique": False}
            ],
            description="杂项工资数据表"
        )

        return templates
    
    def create_salary_table(self, 
                           month: str, 
                           year: int, 
                           custom_columns: Optional[List[ColumnDefinition]] = None) -> bool:
        """
        创建工资数据表
        
        Args:
            month: 月份 (格式: YYYY-MM)
            year: 年份
            custom_columns: 自定义列定义，如果提供则合并到默认列中
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 生成表名
            table_name = f"salary_data_{year}_{month.replace('-', '_')}"
            
            # 检查表是否已存在
            if self.db_manager.table_exists(table_name):
                self.logger.info(f"表 {table_name} 已存在")
                return True
            
            # 获取基础表结构
            base_schema = self._table_templates["salary_data"]
            columns = base_schema.columns.copy()
            
            # 合并自定义列
            if custom_columns:
                columns.extend(custom_columns)
            
            # 创建表结构
            schema = TableSchema(
                table_name=table_name,
                columns=columns,
                primary_key=base_schema.primary_key,
                indexes=base_schema.indexes,
                description=f"{year}年{month}月工资数据表"
            )
            
            # 创建表
            success = self._create_table_from_schema(schema)
            
            if success:
                self.logger.info(f"成功创建工资数据表: {table_name}")
                self._record_table_metadata(schema)
            else:
                self.logger.error(f"创建工资数据表失败: {table_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"创建工资数据表时出错: {e}")
            return False
    
    def create_change_data_table(self, 
                                table_name: str,
                                columns: Optional[List[str]] = None) -> bool:
        """
        创建异动数据表
        
        Args:
            table_name: 表名
            columns: 列名列表（可选，用于动态创建表结构）
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 检查表是否已存在
            if self.db_manager.table_exists(table_name):
                self.logger.warning(f"异动数据表已存在: {table_name}")
                return True
            
            # 获取异动表模板
            # 直接定义异动表模板，因为没有_get_table_templates方法
            # ColumnDefinition和TableSchema在本文件中定义
            # 将异动专用字段设为可空，以支持普通Excel数据导入
            change_columns = [
                ColumnDefinition("id", "INTEGER", False, None, True, "自增主键"),
                ColumnDefinition("employee_id", "TEXT", True, None, False, "员工工号"),
                ColumnDefinition("change_type", "TEXT", True, None, False, "异动类型"),
                ColumnDefinition("field_name", "TEXT", True, None, False, "变化字段"),
                ColumnDefinition("old_value", "TEXT", True, None, False, "旧值"),
                ColumnDefinition("new_value", "TEXT", True, None, False, "新值"),
                ColumnDefinition("change_amount", "REAL", True, "0", False, "变化金额"),
                ColumnDefinition("change_reason", "TEXT", True, None, False, "变化原因"),
                ColumnDefinition("month", "TEXT", True, None, False, "月份"),
                ColumnDefinition("year", "INTEGER", True, None, False, "年份"),
                ColumnDefinition("detected_at", "TEXT", True, None, False, "检测时间"),
                ColumnDefinition("verified", "INTEGER", True, "0", False, "是否验证")
            ]
            
            base_template = TableSchema(
                table_name="salary_changes",
                columns=change_columns,
                primary_key=["id"],
                indexes=[
                    {"name": "idx_change_employee", "columns": ["employee_id"], "unique": False},
                    {"name": "idx_change_type", "columns": ["change_type"], "unique": False},
                    {"name": "idx_change_month", "columns": ["month", "year"], "unique": False}
                ],
                description="工资异动记录表"
            )
            
            # 如果提供了列名，创建动态列定义
            if columns:
                # 保留模板的核心列
                core_columns = ["id", "employee_id", "change_type", "field_name", 
                               "old_value", "new_value", "change_amount", "change_reason",
                               "month", "year", "detected_at", "verified"]
                
                column_definitions = []
                
                # 添加核心列
                for col in base_template.columns:
                    if col.name in core_columns:
                        column_definitions.append(col)
                
                # 添加额外的动态列
                for col_name in columns:
                    if col_name not in core_columns:
                        # 创建动态列定义（默认为TEXT类型）
                        column_definitions.append(
                            ColumnDefinition(col_name, "TEXT", True, None, False, f"动态字段: {col_name}")
                        )
            else:
                # 使用模板的默认列定义
                column_definitions = base_template.columns
            
            # 创建表结构
            schema = TableSchema(
                table_name=table_name,
                columns=column_definitions,
                primary_key=base_template.primary_key,
                indexes=base_template.indexes,
                description=f"异动数据表 - {table_name}"
            )
            
            # 创建表 - 使用execute_query直接创建
            # 构建CREATE TABLE语句
            column_defs = []
            for col in column_definitions:
                col_def = f'"{col.name}" {col.type}'
                # ID列作为主键
                if col.name == 'id' and col.type == 'INTEGER':
                    col_def += ' PRIMARY KEY AUTOINCREMENT'
                elif not col.nullable:
                    col_def += ' NOT NULL'
                if col.default is not None:
                    col_def += f' DEFAULT {col.default}'
                if col.unique and col.name != 'id':
                    col_def += ' UNIQUE'
                column_defs.append(col_def)
            
            column_list = ",\n    ".join(column_defs)
            create_sql = f'CREATE TABLE IF NOT EXISTS "{table_name}" (\n    {column_list}\n)'
            
            try:
                self.db_manager.execute_query(create_sql)
                success = True
            except Exception as e:
                self.logger.error(f"创建表失败: {e}")
                success = False
            
            if success:
                self.logger.info(f"成功创建异动数据表: {table_name}")
                self._record_table_metadata(schema)
            else:
                self.logger.error(f"创建异动数据表失败: {table_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"创建异动数据表时出错: {e}")
            return False
    
    def get_table_record_count(self, table_name: str) -> int:
        """
        获取表的记录总数
        
        Args:
            table_name: 表名
            
        Returns:
            int: 记录总数
        """
        try:
            query = f'SELECT COUNT(*) as count FROM "{table_name}"'
            result = self.db_manager.execute_query(query)
            return result[0]['count'] if result else 0
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 记录数失败: {e}")
            return 0
    
    def create_specialized_salary_table(self,
                                       template_key: str,
                                       month: str,
                                       year: int,
                                       employee_type: str = "") -> bool:
        """
        创建专用工资数据表

        Args:
            template_key: 专用模板键名 (retired_employees, pension_employees, etc.)
            month: 月份 (格式: MM 或 YYYY-MM)
            year: 年份
            employee_type: 员工类型后缀

        Returns:
            bool: 是否创建成功
        """
        try:
            # 生成表名
            month_str = f"{month:02d}" if isinstance(month, int) else str(month).replace('-', '_')
            if employee_type:
                table_name = f"salary_data_{year}_{month_str}_{employee_type}"
            else:
                table_name = f"salary_data_{year}_{month_str}"

            # 检查表是否已存在
            if self.db_manager.table_exists(table_name):
                self.logger.info(f"专用表 {table_name} 已存在")
                return True

            # 获取专用模板
            template_fields = self.specialized_templates.get_template(template_key)
            if not template_fields:
                self.logger.warning(f"未找到专用模板 {template_key}，使用通用模板")
                return self.create_salary_table(month, year)

            # 转换为ColumnDefinition格式
            columns = []
            for field in template_fields:
                columns.append(ColumnDefinition(
                    name=field.name,
                    type=field.type,
                    nullable=field.nullable,
                    default=field.default,
                    unique=field.unique,
                    description=field.description
                ))

            # 创建表结构
            schema = TableSchema(
                table_name=table_name,
                columns=columns,
                primary_key=["id"],
                indexes=[],
                description=f"{year}年{month}月{self.specialized_templates._get_template_description(template_key)}"
            )

            # 执行创建
            success = self._create_table_from_schema(schema)

            if success:
                # 记录表元信息
                self._record_table_metadata(schema)
                self.logger.info(f"专用工资数据表创建成功: {table_name} (模板: {template_key})")

            return success

        except Exception as e:
            self.logger.error(f"创建专用工资数据表失败: {e}")
            return False
    
    def _quote_identifier(self, identifier: str) -> str:
        """
        为SQL标识符添加引号，确保中文和特殊字符能正确处理
        
        Args:
            identifier: 标识符（表名、列名等）
            
        Returns:
            str: 带引号的标识符
        """
        # SQLite使用双引号包围标识符
        # 如果标识符中包含双引号，需要转义
        escaped = identifier.replace('"', '""')
        return f'"{escaped}"'
    
    def _calculate_schema_hash(self, schema: TableSchema) -> str:
        """
        计算表结构哈希值
        
        Args:
            schema: 表结构定义
            
        Returns:
            str: 哈希值
        """
        schema_str = self._schema_to_json(schema)
        return hashlib.md5(schema_str.encode('utf-8')).hexdigest()
    
    def _schema_to_json(self, schema: TableSchema) -> str:
        """
        将表结构转换为JSON字符串
        
        Args:
            schema: 表结构定义
            
        Returns:
            str: JSON字符串
        """
        schema_dict = {
            "table_name": schema.table_name,
            "columns": [
                {
                    "name": col.name,
                    "type": col.type,
                    "nullable": col.nullable,
                    "default": col.default,
                    "unique": col.unique,
                    "description": col.description
                }
                for col in schema.columns
            ],
            "primary_key": schema.primary_key,
            "indexes": schema.indexes,
            "version": schema.version,
            "description": schema.description
        }
        
        return json.dumps(schema_dict, ensure_ascii=False, sort_keys=True)

    def get_dataframe_paginated(self, table_name: str, page: int = 1, page_size: int = 100) -> Tuple[Optional[pd.DataFrame], int]:
        """
        分页查询表数据
        
        Args:
            table_name (str): 表名
            page (int): 页码(从1开始)
            page_size (int): 每页记录数
            
        Returns:
            Tuple[Optional[pd.DataFrame], int]: (页面数据, 总记录数)
        """
        self.logger.info(f"正在从表 {table_name} 分页获取数据: 第{page}页, 每页{page_size}条")
        
        if not self.table_exists(table_name):
            self.logger.warning(f"表 {table_name} 不存在，无法获取数据。")
            return None, 0
        
        try:
            # 获取总记录数
            total_count = self.get_table_record_count(table_name)
            
            if total_count == 0:
                self.logger.info(f"表 {table_name} 为空。")
                return pd.DataFrame(), 0
            
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 构建分页查询SQL
            query = f'SELECT * FROM "{table_name}" LIMIT {page_size} OFFSET {offset}'
            # 执行并记录耗时（慢查询阈值）
            try:
                import time as _t, os as _os
                _t0 = _t.time()
                data = self.db_manager.execute_query(query)
                _elapsed_ms = int((_t.time() - _t0) * 1000)
                _slow_ms = int(_os.getenv('LOG_SLOW_SQL_MS', '300'))
                if _elapsed_ms >= _slow_ms:
                    self.logger.info(f"[PERF][SLOW_SQL] {table_name} page={page}, size={page_size}, elapsed={_elapsed_ms}ms")
            except Exception:
                data = self.db_manager.execute_query(query)
            
            if data:
                df = pd.DataFrame(data)
                self.logger.info(f"成功从表 {table_name} 获取第{page}页数据: {len(df)} 行，总计{total_count}行")
                return df, total_count
            else:
                self.logger.info(f"表 {table_name} 第{page}页无数据。")
                return pd.DataFrame(), total_count
                
        except Exception as e:
            self.logger.error(f"从表 {table_name} 分页获取数据时出错: {e}", exc_info=True)
            return None, 0

    def get_dataframe_paginated_with_sort(self, table_name: str, page: int = 1, page_size: int = 100, 
                                        sort_columns: Optional[List[Dict[str, Any]]] = None) -> Tuple[Optional[pd.DataFrame], int]:
        """
        支持排序的分页查询表数据
        
        Args:
            table_name (str): 表名
            page (int): 页码(从1开始)
            page_size (int): 每页记录数
            sort_columns (List[Dict], optional): 排序列信息
                格式: [{'column_name': '列名', 'order': 'ascending'/'descending'}]
            
        Returns:
            Tuple[Optional[pd.DataFrame], int]: (页面数据, 总记录数)
        """
        self.logger.info(f"正在从表 {table_name} 分页获取数据（支持排序）: 第{page}页, 每页{page_size}条, 排序={len(sort_columns) if sort_columns else 0}列")
        
        if not self.table_exists(table_name):
            self.logger.warning(f"表 {table_name} 不存在，无法获取数据。")
            return None, 0
        
        try:
            # 获取总记录数
            total_count = self.get_table_record_count(table_name)
            
            if total_count == 0:
                self.logger.info(f"表 {table_name} 为空。")
                return pd.DataFrame(), 0
            
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 构建分页查询SQL
            query = f'SELECT * FROM "{table_name}"'
            
            # 添加排序子句
            if sort_columns:
                order_clauses = []
                for sort_col in sort_columns:
                    column_name = sort_col.get('column_name', '')
                    order = sort_col.get('order', 'ascending')
                    
                    if column_name:
                        # [排序修复] 记录要排序的列名用于调试（降级为DEBUG）
                        self.logger.debug(f"[排序修复] 准备排序列: {column_name}")
                        
                        # [排序修复] 验证列是否存在
                        if not self._column_exists_in_table(table_name, column_name):
                            self.logger.warning(f"[排序修复] 列 '{column_name}' 不存在，跳过排序")
                            continue
                        
                        self.logger.debug(f"[排序修复] 列 '{column_name}' 验证通过，开始排序")
                        
                        # SQL注入防护：确保列名安全
                        safe_column_name = column_name.replace('"', '""')
                        order_direction = 'ASC' if order == 'ascending' else 'DESC'
                        
                        # 对数值列进行特殊处理：尝试转换为数值类型排序
                        if self._is_numeric_column(table_name, column_name):
                            order_clauses.append(f'CAST("{safe_column_name}" AS REAL) {order_direction}')
                            self.logger.debug(f"[FIX] [排序修复] 数值列排序: {safe_column_name} {order_direction}")
                        else:
                            order_clauses.append(f'"{safe_column_name}" {order_direction}')
                            self.logger.debug(f"[FIX] [排序修复] 文本列排序: {safe_column_name} {order_direction}")
                
                if order_clauses:
                    query += f' ORDER BY {", ".join(order_clauses)}'
                    self.logger.debug(f"[FIX] [排序修复] 添加排序子句: {', '.join(order_clauses)}")
                else:
                    self.logger.warning(f"[FIX] [排序修复] 所有排序列都无效，跳过排序")
            
            # 添加分页子句
            query += f' LIMIT {page_size} OFFSET {offset}'
            
            # 记录完整SQL（受策略/环境控制；默认降噪到DEBUG并去除print）
            try:
                import os as _os
                from src.utils.logging_utils import log_throttle
                if _os.getenv("LOG_SQL_DETAIL", "0") == "1":
                    # 细节开启时，2s节流一次
                    if log_throttle('sql-query-detail', 2.0):
                        self.logger.debug(f"[SQL] {query}")
                else:
                    # 默认仅在DEBUG级别记录并节流
                    if log_throttle('sql-query-brief', 5.0):
                        self.logger.debug(f"[SQL brief] {table_name} page={page}, size={page_size}, sort={len(sort_columns) if sort_columns else 0}")
            except Exception:
                # 回退：不打印到stdout，避免阻塞与噪音
                pass
            # 执行并记录耗时（慢查询阈值）
            try:
                import time as _t, os as _os
                _t0 = _t.time()
                data = self.db_manager.execute_query(query)
                _elapsed_ms = int((_t.time() - _t0) * 1000)
                _slow_ms = int(_os.getenv('LOG_SLOW_SQL_MS', '300'))
                if _elapsed_ms >= _slow_ms:
                    self.logger.info(f"[PERF][SLOW_SQL] {table_name} page={page}, size={page_size}, sort={len(sort_columns) if sort_columns else 0}, elapsed={_elapsed_ms}ms")
            except Exception:
                data = self.db_manager.execute_query(query)
            
            if data:
                df = pd.DataFrame(data)
                
                # [P0-CRITICAL修复] 记录返回的工号数据以验证分页是否正确（脱敏）
                try:
                    if 'employee_id' in df.columns:
                        import os as _os
                        from src.utils.logging_utils import log_throttle, redact
                        if _os.getenv("LOG_SQL_DETAIL", "0") == "1" and log_throttle('sql-ids-sample', 5.0):
                            first_5_ids = [redact(str(v)) for v in df['employee_id'].head(5).tolist()]
                            self.logger.debug(f"[SQL] sample employee_id: {first_5_ids}")
                except Exception:
                    pass
                
                # [FIX] [P0-CRITICAL修复] 记录查询结果中排序列的数据
                try:
                    if sort_columns and not df.empty:
                        import os as _os
                        from src.utils.logging_utils import log_throttle
                        if _os.getenv("LOG_SQL_DETAIL", "0") == "1" and log_throttle('sql-sort-sample', 5.0):
                            for sort_col in sort_columns:
                                column_name = sort_col.get('column_name', '')
                                if column_name in df.columns:
                                    # 脱敏保护：若字段含疑似敏感数字，做掩码
                                    from src.utils.logging_utils import redact
                                    values = [redact(str(v)) for v in df[column_name].head(10).tolist()]
                                    self.logger.debug(f"[SQL] {column_name} sample: {values}")
                except Exception:
                    pass
                
                self.logger.info(f"成功从表 {table_name} 获取第{page}页数据（含排序）: {len(df)} 行，总计{total_count}行")
                return df, total_count
            else:
                self.logger.info(f"表 {table_name} 第{page}页无数据。")
                return pd.DataFrame(), total_count
                
        except Exception as e:
            self.logger.error(f"从表 {table_name} 分页获取数据时出错（含排序）: {e}", exc_info=True)
            return None, 0
    
    def _is_numeric_column(self, table_name: str, column_name: str) -> bool:
        """
        判断列是否为数值类型
        
        Args:
            table_name: 表名
            column_name: 列名
            
        Returns:
            bool: 是否为数值类型
        """
        try:
            # 查询列的数据类型信息
            query = f'PRAGMA table_info("{table_name}")'
            schema_info = self.db_manager.execute_query(query)
            
            for column_info in schema_info:
                if column_info['name'] == column_name:  # 字典格式访问列名
                    column_type = column_info['type'].upper()  # 字典格式访问数据类型
                    return column_type in ['INTEGER', 'REAL', 'NUMERIC', 'DECIMAL']
            
            # 如果没有找到列信息，尝试检查样本数据
            sample_query = f'SELECT "{column_name}" FROM "{table_name}" WHERE "{column_name}" IS NOT NULL LIMIT 10'
            sample_data = self.db_manager.execute_query(sample_query)
            
            if sample_data:
                # 检查样本数据是否都是数值
                for row in sample_data:
                    value = row[column_name]  # 字典格式访问列值
                    if value is not None:
                        try:
                            float(str(value).replace(',', ''))  # 移除千分位分隔符后尝试转换
                        except (ValueError, TypeError):
                            return False
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"判断列 {column_name} 数据类型失败: {e}")
            return False
    
    def _column_exists_in_table(self, table_name: str, column_name: str) -> bool:
        """
        检查列是否存在于指定表中
        
        Args:
            table_name: 表名
            column_name: 列名
            
        Returns:
            bool: 列是否存在
        """
        try:
            # [FIX] [排序修复] 使用PRAGMA table_info更可靠地检查列是否存在
            table_info_query = f'PRAGMA table_info("{table_name}")'
            table_info = self.db_manager.execute_query(table_info_query)
            
            # 检查列是否在表结构中
            for column_info in table_info:
                # DatabaseManager 返回的是字典格式
                if column_info.get('name') == column_name:
                    column_type = column_info.get('type', 'UNKNOWN')
                    self.logger.info(f"[FIX] [排序修复] 列 '{column_name}' 存在于表 '{table_name}' 中（类型: {column_type}）")
                    return True
            
            self.logger.warning(f"[FIX] [排序修复] 列 '{column_name}' 不存在于表 '{table_name}' 中")
            return False
            
        except Exception as e:
            self.logger.error(f"[FIX] [排序修复] 检查列 '{column_name}' 是否存在时发生错误: {e}")
            # [FIX] [排序修复] 如果PRAGMA失败，降级到原来的查询方法
            try:
                test_query = f'SELECT "{column_name}" FROM "{table_name}" LIMIT 1'
                self.db_manager.execute_query(test_query)
                self.logger.info(f"[FIX] [排序修复] 列 '{column_name}' 存在于表 '{table_name}' 中（降级检查成功）")
                return True
            except Exception as fallback_e:
                self.logger.warning(f"[FIX] [排序修复] 列 '{column_name}' 不存在于表 '{table_name}' 中（降级检查也失败）: {fallback_e}")
                return False
    
    def _create_table_from_schema(self, schema: TableSchema) -> bool:
        """
        根据表结构创建表
        
        Args:
            schema: 表结构定义
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 构建CREATE TABLE语句
            columns_sql = []
            for col in schema.columns:
                col_sql = f'"{col.name}" {col.type}'
                
                if not col.nullable:
                    col_sql += ' NOT NULL'
                
                if col.default is not None:
                    col_sql += f' DEFAULT {col.default}'
                
                if col.unique:
                    col_sql += ' UNIQUE'
                
                columns_sql.append(col_sql)
            
            # 添加主键约束
            if schema.primary_key:
                pk_columns = ', '.join([f'"{pk}"' for pk in schema.primary_key])
                columns_sql.append(f'PRIMARY KEY ({pk_columns})')
            
            create_sql = f'''
            CREATE TABLE "{schema.table_name}" (
                {', '.join(columns_sql)}
            )
            '''
            
            # 执行创建表语句
            self.db_manager.execute_update(create_sql)
            self.logger.info(f"成功创建表: {schema.table_name}")
            
            # 创建索引
            for index_def in schema.indexes:
                self._create_index(schema.table_name, index_def)
            
            return True
            
        except Exception as e:
            self.logger.error(f"创建表 {schema.table_name} 失败: {e}")
            return False
    
    def _create_index(self, table_name: str, index_def: Dict[str, Any]) -> bool:
        """
        创建表索引
        
        Args:
            table_name: 表名
            index_def: 索引定义 {'name': 'idx_name', 'columns': ['col1', 'col2'], 'unique': False}
            
        Returns:
            bool: 是否创建成功
        """
        try:
            index_name = index_def['name']
            columns = index_def['columns']
            is_unique = index_def.get('unique', False)
            
            unique_clause = 'UNIQUE' if is_unique else ''
            columns_clause = ', '.join([f'"{col}"' for col in columns])
            
            create_index_sql = f'''
            CREATE {unique_clause} INDEX "{index_name}" 
            ON "{table_name}" ({columns_clause})
            '''
            
            self.db_manager.execute_update(create_index_sql)
            self.logger.info(f"成功创建索引: {index_name} on {table_name}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            return False
    
    def _record_table_metadata(self, schema: TableSchema) -> None:
        """
        记录表元信息
        
        Args:
            schema: 表结构定义
        """
        try:
            # 生成表结构哈希值
            schema_hash = self._calculate_schema_hash(schema)
            
            metadata_sql = """
                INSERT INTO table_metadata 
                (table_name, table_type, schema_version, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            
            current_time = datetime.now().isoformat()
            
            # 根据表名确定表类型
            table_type = "unknown"
            if "salary_data" in schema.table_name:
                table_type = "salary_data"
            elif "change_data" in schema.table_name:
                table_type = "change_data"
            elif "misc_data" in schema.table_name:
                table_type = "misc_data"

            params = (
                schema.table_name,
                table_type,
                schema.version,
                schema.description,
                current_time,
                current_time
            )
            
            self.db_manager.execute_update(metadata_sql, params)
            
            # 保存详细的表结构信息到系统配置
            schema_key = f"table_schema_{schema.table_name}"
            schema_json = self._schema_to_json(schema)
            
            config_sql = """
                INSERT OR REPLACE INTO system_config (key, value, description, updated_at)
                VALUES (?, ?, ?, ?)
            """
            
            config_params = (
                schema_key,
                schema_json,
                f"表结构信息: {schema.table_name}",
                current_time
            )
            
            self.db_manager.execute_update(config_sql, config_params)
            
        except Exception as e:
            self.logger.error(f"记录表元信息失败: {str(e)}")

    def get_dataframe_from_table(self, table_name: str) -> Optional[pd.DataFrame]:
        """
        从表中获取完整数据
        
        Args:
            table_name: 表名
            
        Returns:
            Optional[pd.DataFrame]: 表数据
        """
        self.logger.info(f"正在从表 {table_name} 获取全部数据")
        
        if not self.table_exists(table_name):
            self.logger.warning(f"表 {table_name} 不存在，无法获取数据。")
            return None
        
        try:
            query = f'SELECT * FROM "{table_name}"'
            data = self.db_manager.execute_query(query)
            
            if data:
                df = pd.DataFrame(data)
                self.logger.info(f"成功从表 {table_name} 获取数据: {len(df)} 行")
                return df
            else:
                self.logger.info(f"表 {table_name} 为空")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"从表 {table_name} 获取数据时出错: {e}")
            return None
    
    def get_table_list(self, table_type: Optional[str] = None, enable_retry: bool = True) -> List[Dict[str, Any]]:
        """
        🔧 [深度修复] 获取指定类型的数据表列表（基于元数据查询的全新实现）

        Args:
            table_type: 表类型，如 'salary_data', 'change_data' 等
            enable_retry: 是否启用智能重试机制

        Returns:
            表信息列表
        """
        # 🆕 [深度修复] 增强重试机制：更多重试次数，更智能的延迟策略
        max_retries = 5 if enable_retry else 1
        base_delay = 0.1  # 基础延迟100ms

        # 🆕 [深度修复] 记录开始时间用于超时控制
        import time
        start_time = time.time()
        max_wait_time = 3.0  # 最大等待3秒

        for attempt in range(max_retries):
            try:
                # 🆕 [深度修复] 检查是否超时
                if time.time() - start_time > max_wait_time:
                    self.logger.warning(f"🔧 [深度修复] 表查询超时 ({max_wait_time}s)，使用当前结果")
                    break

                # 🔧 [深度修复] 增强数据库就绪检查
                database_ready = self._ensure_database_ready()
                if not database_ready:
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)  # 指数退避
                        self.logger.debug(f"🔧 [深度修复] 数据库未就绪，等待 {delay:.1f}s 后重试 ({attempt + 1}/{max_retries})")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.warning("🔧 [深度修复] 数据库就绪检查失败，使用降级查询")

                # 🔧 [深度修复] 核心修复：基于元数据查询而非表名前缀过滤
                table_metadata_list = self._query_table_metadata_by_type(table_type)

                if not table_metadata_list:
                    # 🆕 [深度修复] 统一的重试逻辑，适用于所有表类型
                    if attempt < max_retries - 1 and table_type in ['salary_data', 'change_data']:
                        # 检查是否有实际表但元数据缺失
                        actual_tables_exist = self._check_actual_tables_exist(table_type)
                        if actual_tables_exist:
                            self.logger.warning(f"🔧 [深度修复] 检测到 {table_type} 类型的实际表但元数据缺失，执行强制同步后重试")
                            # 执行更强的同步操作
                            self._force_database_sync()
                            # 额外等待确保同步完成
                            time.sleep(0.2)
                            continue
                        else:
                            self.logger.debug(f"🔧 [深度修复] 确实没有 {table_type} 类型的表，这是正常状态")
                            break
                    else:
                        self.logger.info(f"🔧 [深度修复] 找到 0 个匹配类型 '{table_type}' 的表 (尝试 {attempt + 1}/{max_retries})")
                        break

                # 🔧 [深度修复] 验证元数据中的表在数据库中确实存在
                validated_metadata = self._validate_metadata_tables_exist(table_metadata_list)

                self.logger.info(f"🔧 [深度修复] 找到 {len(validated_metadata)} 个匹配类型 '{table_type}' 的表 (尝试 {attempt + 1}/{max_retries})")

                # 🔧 [深度修复] 返回验证后的表信息
                return validated_metadata

            except Exception as e:
                self.logger.warning(f"🔧 [深度修复] 表查询尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    import time
                    # 退避等待（固定200ms步进），避免未定义retry_delay
                    time.sleep(0.2 * (attempt + 1))
                    continue
                else:
                    self.logger.error(f"🔧 [深度修复] 所有重试都失败，返回空列表")
                    return []

        # 如果所有重试都失败，返回空列表
        return []

    def _query_table_metadata_by_type(self, table_type: Optional[str]) -> List[Dict[str, Any]]:
        """
        🔧 [深度修复] 基于元数据查询指定类型的表信息

        Args:
            table_type: 表类型，如 'salary_data', 'change_data' 等

        Returns:
            表元数据信息列表
        """
        try:
            if table_type:
                # 查询指定类型的表元数据
                query = """
                SELECT table_name, table_type, description, created_at, updated_at
                FROM table_metadata
                WHERE table_type = ? AND is_active = 1
                ORDER BY table_name
                """
                metadata_rows = self.db_manager.execute_query(query, (table_type,))
            else:
                # 查询所有表元数据
                query = """
                SELECT table_name, table_type, description, created_at, updated_at
                FROM table_metadata
                WHERE is_active = 1
                ORDER BY table_name
                """
                metadata_rows = self.db_manager.execute_query(query)

            if not metadata_rows:
                return []

            # 转换为标准格式并解析表信息
            table_list = []
            for row in metadata_rows:
                table_info = {
                    'table_name': row['table_name'],
                    'table_type': row['table_type'],
                    'description': row.get('description', ''),
                    'created_at': row.get('created_at', ''),
                    'updated_at': row.get('updated_at', ''),
                    'version': 1,
                    'schema_hash': ''
                }

                # 🔧 [深度修复] 解析表名获取结构化信息
                parsed_info = self._parse_table_name_info(row['table_name'], row['table_type'])
                if parsed_info:
                    table_info.update(parsed_info)

                table_list.append(table_info)

            return table_list

        except Exception as e:
            self.logger.error(f"🔧 [深度修复] 查询表元数据失败: {e}")
            return []

    def _parse_table_name_info(self, table_name: str, table_type: str) -> Optional[Dict[str, Any]]:
        """
        🔧 [深度修复] 解析表名获取结构化信息（支持所有表类型）

        Args:
            table_name: 表名
            table_type: 表类型

        Returns:
            解析后的信息字典
        """
        try:
            # 定义员工类型映射
            employee_type_map = {
                'retired_employees': '离休人员',
                'pension_employees': '退休人员',
                'active_employees': '全部在职人员',
                'a_grade_employees': 'A岗职工'
            }

            if table_type == 'salary_data' and table_name.startswith('salary_data_'):
                # 解析工资表：salary_data_YYYY_MM_type
                return self._parse_salary_table_name(table_name, employee_type_map)
            elif table_type == 'change_data' and table_name.startswith('change_data_'):
                # 🆕 [深度修复] 解析异动表：change_data_YYYY_MM_type
                return self._parse_change_table_name(table_name, employee_type_map)
            else:
                # 其他类型表的默认处理
                return {}

        except Exception as e:
            self.logger.warning(f"🔧 [深度修复] 解析表名失败 {table_name}: {e}")
            return {}

    def _parse_salary_table_name(self, table_name: str, employee_type_map: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析工资表名"""
        parsed = self._parse_table_name(table_name)
        if parsed:
            year, month, employee_type = parsed
            display_name = employee_type_map.get(employee_type, employee_type)
            return {
                'year': year,
                'month': month,
                'employee_type': employee_type,
                'display_name': display_name,
                'description': f"{year}年{month:02d}月{display_name}工资数据"
            }
        return {}

    def _parse_change_table_name(self, table_name: str, employee_type_map: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        🆕 [深度修复] 解析异动表名
        格式：change_data_YYYY_MM_type
        """
        try:
            # 移除前缀 'change_data_'
            name_part = table_name[12:]  # len('change_data_') = 12
            parts = name_part.split('_')

            if len(parts) >= 3:
                year_str = parts[0]
                month_str = parts[1]
                employee_type = '_'.join(parts[2:])

                # 转换为数字
                year = int(year_str)
                month = int(month_str)

                # 获取显示名称
                display_name = employee_type_map.get(employee_type, employee_type)

                return {
                    'year': year,
                    'month': month,
                    'employee_type': employee_type,
                    'display_name': display_name,
                    'description': f"{year}年{month:02d}月{display_name}异动数据"
                }
            else:
                self.logger.warning(f"🔧 [深度修复] 异动表名格式不正确: {table_name}")
                return {}

        except (ValueError, IndexError) as e:
            self.logger.warning(f"🔧 [深度修复] 解析异动表名失败 {table_name}: {e}")
            return {}

    def _check_actual_tables_exist(self, table_type: str) -> bool:
        """
        🔧 [深度修复] 检查指定类型的实际表是否存在（用于检测元数据缺失情况）

        Args:
            table_type: 表类型

        Returns:
            是否存在实际表
        """
        try:
            # 从sqlite_master查询实际表
            all_table_names = self._execute_table_query_with_fallback()
            if not all_table_names:
                return False

            # 根据表类型检查是否有匹配的表名
            prefix = f"{table_type}_"
            matching_tables = [name for name in all_table_names if name.startswith(prefix)]

            return len(matching_tables) > 0

        except Exception as e:
            self.logger.warning(f"🔧 [深度修复] 检查实际表存在性失败: {e}")
            return False

    def _validate_metadata_tables_exist(self, metadata_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        🔧 [深度修复] 验证元数据中的表在数据库中确实存在

        Args:
            metadata_list: 元数据表信息列表

        Returns:
            验证后的表信息列表（只包含实际存在的表）
        """
        if not metadata_list:
            return []

        try:
            # 获取所有实际存在的表名
            all_table_names = self._execute_table_query_with_fallback()
            if not all_table_names:
                self.logger.warning("🔧 [深度修复] 无法获取实际表列表，返回空结果")
                return []

            # 过滤出实际存在的表
            validated_list = []
            for table_info in metadata_list:
                table_name = table_info.get('table_name', '')
                if table_name in all_table_names:
                    # 🆕 [深度修复] 获取实际表的记录数
                    try:
                        count_query = f"SELECT COUNT(*) as count FROM `{table_name}`"
                        count_result = self.db_manager.execute_query(count_query)
                        if count_result:
                            table_info['record_count'] = count_result[0]['count']
                        else:
                            table_info['record_count'] = 0
                    except Exception as e:
                        self.logger.warning(f"🔧 [深度修复] 获取表 {table_name} 记录数失败: {e}")
                        table_info['record_count'] = 0

                    validated_list.append(table_info)
                else:
                    self.logger.warning(f"🔧 [深度修复] 元数据中的表 {table_name} 在数据库中不存在，跳过")

            return validated_list

        except Exception as e:
            self.logger.error(f"🔧 [深度修复] 验证元数据表存在性失败: {e}")
            return []

    def _parse_table_list(self, table_names: List[str], table_type: Optional[str]) -> List[Dict[str, Any]]:
        """
        🔧 [深度修复] 解析表名列表为表信息（支持所有表类型）

        Args:
            table_names: 表名列表
            table_type: 表类型

        Returns:
            表信息列表
        """
        table_list = []

        # 定义员工类型映射
        employee_type_map = {
            'retired_employees': '离休人员',
            'pension_employees': '退休人员',
            'active_employees': '全部在职人员',
            'a_grade_employees': 'A岗职工'
        }

        for name in table_names:
            table_info = {
                'table_name': name,
                'description': '',
                'version': 1,
                'schema_hash': ''
            }

            # 🔧 [深度修复] 支持salary_data类型的表
            if table_type == 'salary_data' and name.startswith('salary_data_'):
                parsed = self._parse_table_name(name)
                if parsed:
                    year, month, employee_type = parsed
                    table_info['year'] = year
                    table_info['month'] = month
                    table_info['employee_type'] = employee_type

                    # 生成友好的显示名称
                    display_name = employee_type_map.get(employee_type, employee_type)
                    table_info['display_name'] = display_name
                    table_info['description'] = f"{year}年{month:02d}月{display_name}工资数据"

            # 🆕 [深度修复] 支持change_data类型的表
            elif table_type == 'change_data' and name.startswith('change_data_'):
                parsed_info = self._parse_change_table_name(name, employee_type_map)
                if parsed_info:
                    table_info.update(parsed_info)

            table_list.append(table_info)

        return table_list

    def _ensure_database_ready(self) -> bool:
        """
        🔧 [P1修复] 确保数据库完全就绪（增强版本）

        Returns:
            bool: 数据库是否就绪
        """
        try:
            # 1. 检查数据库连接
            if not self._check_database_connection():
                self.logger.debug("🔧 [P1修复] 数据库连接检查失败")
                return False

            # 2. 检查WAL模式状态并强制同步
            if not self._check_wal_mode_status():
                self.logger.debug("🔧 [P1修复] WAL模式检查失败")
                return False

            # 3. 🆕 [P1修复] 强制执行完整WAL checkpoint确保数据一致性
            if not self._perform_full_database_sync():
                self.logger.debug("🔧 [P1修复] 完整数据库同步失败")
                return False

            # 4. 🆕 [P1修复] 验证数据库状态一致性
            if not self._verify_database_consistency():
                self.logger.debug("🔧 [P1修复] 数据库一致性验证失败")
                return False

            # 5. 🆕 [P1修复] 验证表元数据可访问性
            if not self._verify_table_metadata_accessibility():
                self.logger.debug("🔧 [P1修复] 表元数据可访问性验证失败")
                return False

            self.logger.debug("🔧 [P1修复] 数据库就绪检查全部通过")
            return True

        except Exception as e:
            self.logger.warning(f"🔧 [P1修复] 数据库就绪检查失败: {e}")
            return False

    def _check_database_connection(self) -> bool:
        """检查数据库连接状态"""
        try:
            # 执行简单查询验证连接
            result = self.db_manager.execute_query("SELECT 1 as test")
            return result is not None and len(result) > 0
        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] 数据库连接检查失败: {e}")
            return False

    def _check_wal_mode_status(self) -> bool:
        """检查WAL模式状态"""
        try:
            # 检查journal模式
            result = self.db_manager.execute_query("PRAGMA journal_mode")
            if result and len(result) > 0:
                mode = result[0].get('journal_mode', '').lower()
                if mode == 'wal':
                    # 检查WAL文件状态
                    wal_info = self.db_manager.execute_query("PRAGMA wal_checkpoint(PASSIVE)")
                    return wal_info is not None
                else:
                    # 非WAL模式，直接返回True
                    return True
            return False
        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] WAL模式检查失败: {e}")
            return False

    def _perform_lightweight_sync(self) -> bool:
        """执行轻量级数据库同步"""
        try:
            # 使用PASSIVE模式的checkpoint，不会阻塞
            result = self.db_manager.execute_query("PRAGMA wal_checkpoint(PASSIVE)")
            return result is not None
        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] 轻量级同步失败: {e}")
            return False

    def _perform_full_database_sync(self) -> bool:
        """🆕 [P1修复] 执行完整数据库同步，确保WAL数据完全写入主数据库"""
        try:
            # 执行完整的WAL checkpoint
            result = self.db_manager.execute_query("PRAGMA wal_checkpoint(FULL)")
            if result and len(result) > 0:
                checkpoint_info = result[0]
                busy = checkpoint_info.get('busy', 0)
                log_pages = checkpoint_info.get('log', 0)
                checkpointed_pages = checkpoint_info.get('checkpointed', 0)

                self.logger.debug(f"🔧 [P1修复] WAL checkpoint完成: busy={busy}, log={log_pages}, checkpointed={checkpointed_pages}")

                # 如果有页面正在使用，等待一小段时间后重试
                if busy > 0:
                    self.logger.debug("🔧 [P1修复] 检测到忙碌页面，等待后重试")
                    import time
                    time.sleep(0.1)
                    # 重试一次
                    result = self.db_manager.execute_query("PRAGMA wal_checkpoint(FULL)")

                return True
            return False
        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] 完整数据库同步失败: {e}")
            return False

    def _verify_database_consistency(self) -> bool:
        """🆕 [P1修复] 验证数据库状态一致性"""
        try:
            # 1. 检查数据库完整性
            result = self.db_manager.execute_query("PRAGMA integrity_check")
            if not result or result[0].get('integrity_check', '').lower() != 'ok':
                self.logger.warning("🔧 [P1修复] 数据库完整性检查失败")
                return False

            # 2. 验证关键表存在
            result = self.db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table' AND name='table_metadata'")
            if not result:
                self.logger.warning("🔧 [P1修复] 关键表table_metadata不存在")
                return False

            # 3. 验证表元数据表可访问
            result = self.db_manager.execute_query("SELECT COUNT(*) as count FROM table_metadata")
            if result is None:
                self.logger.warning("🔧 [P1修复] 无法访问table_metadata表")
                return False

            metadata_count = result[0].get('count', 0)
            self.logger.debug(f"🔧 [P1修复] 表元数据记录数: {metadata_count}")

            return True
        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] 数据库一致性验证失败: {e}")
            return False

    def _verify_table_metadata_accessibility(self) -> bool:
        """🆕 [深度修复] 验证表元数据的可访问性（支持所有表类型）"""
        try:
            # 🔧 [深度修复] 查询salary_data类型的表元数据
            salary_query = """
            SELECT table_name, table_type FROM table_metadata
            WHERE table_type = 'salary_data'
            ORDER BY table_name
            """
            salary_result = self.db_manager.execute_query(salary_query)

            if salary_result is None:
                self.logger.debug("🔧 [深度修复] salary_data表元数据查询返回None")
                return False

            salary_tables_count = len(salary_result)
            self.logger.debug(f"🔧 [深度修复] 在table_metadata中找到 {salary_tables_count} 个salary_data类型的表")

            # 🆕 [深度修复] 查询change_data类型的表元数据
            change_query = """
            SELECT table_name, table_type FROM table_metadata
            WHERE table_type = 'change_data'
            ORDER BY table_name
            """
            change_result = self.db_manager.execute_query(change_query)

            if change_result is None:
                self.logger.debug("🔧 [深度修复] change_data表元数据查询返回None")
                # 对于change_data，如果查询失败但不是致命错误，继续检查
                change_tables_count = 0
            else:
                change_tables_count = len(change_result)

            self.logger.debug(f"🔧 [深度修复] 在table_metadata中找到 {change_tables_count} 个change_data类型的表")

            # 🆕 [深度修复] 验证元数据与实际表的一致性
            if salary_tables_count > 0:
                # 验证salary_data表的一致性
                if not self._verify_metadata_table_consistency('salary_data', salary_result):
                    self.logger.warning("🔧 [深度修复] salary_data表元数据与实际表不一致")
                    return False

            if change_tables_count > 0:
                # 验证change_data表的一致性
                if not self._verify_metadata_table_consistency('change_data', change_result):
                    self.logger.warning("🔧 [深度修复] change_data表元数据与实际表不一致")
                    return False

            self.logger.debug(f"🔧 [深度修复] 表元数据可访问性验证通过: salary_data={salary_tables_count}, change_data={change_tables_count}")
            return True

        except Exception as e:
            self.logger.debug(f"🔧 [深度修复] 表元数据可访问性验证失败: {e}")
            return False

    def _verify_metadata_table_consistency(self, table_type: str, metadata_rows: List[Dict[str, Any]]) -> bool:
        """
        🆕 [深度修复] 验证元数据与实际表的一致性

        Args:
            table_type: 表类型
            metadata_rows: 元数据查询结果

        Returns:
            是否一致
        """
        try:
            if not metadata_rows:
                return True  # 空结果认为是一致的

            # 获取所有实际存在的表名
            all_actual_tables = self._execute_table_query_with_fallback()
            if not all_actual_tables:
                self.logger.warning(f"🔧 [深度修复] 无法获取实际表列表，跳过 {table_type} 一致性验证")
                return True  # 无法验证时不阻塞

            # 检查元数据中的每个表是否在实际表中存在
            missing_tables = []
            for row in metadata_rows:
                table_name = row.get('table_name', '')
                if table_name and table_name not in all_actual_tables:
                    missing_tables.append(table_name)

            if missing_tables:
                self.logger.warning(f"🔧 [深度修复] {table_type} 类型的表元数据与实际表不一致，缺失表: {missing_tables}")
                return False

            self.logger.debug(f"🔧 [深度修复] {table_type} 类型的表元数据与实际表一致")
            return True

        except Exception as e:
            self.logger.warning(f"🔧 [深度修复] 验证 {table_type} 表一致性失败: {e}")
            return True  # 验证失败时不阻塞

    def _execute_table_query_with_fallback(self) -> List[str]:
        """
        🔧 [P1修复] 执行表查询，带降级处理

        Returns:
            List[str]: 表名列表
        """
        try:
            # 主查询：从sqlite_master获取表信息
            query = """
            SELECT name FROM sqlite_master
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
            """

            all_table_rows = self.db_manager.execute_query(query)

            if all_table_rows:
                table_names = [row['name'] for row in all_table_rows]
                self.logger.debug(f"🔧 [P1修复] 主查询成功，找到 {len(table_names)} 个表")
                return table_names
            else:
                self.logger.debug("🔧 [P1修复] 主查询返回空结果，尝试降级查询")
                return self._fallback_table_query()

        except Exception as e:
            self.logger.warning(f"🔧 [P1修复] 主表查询失败: {e}，使用降级查询")
            return self._fallback_table_query()

    def _fallback_table_query(self) -> List[str]:
        """降级表查询方法"""
        try:
            # 降级查询：直接查询表元数据
            metadata_query = """
            SELECT DISTINCT table_name FROM table_metadata
            WHERE table_name IS NOT NULL
            ORDER BY table_name
            """

            metadata_rows = self.db_manager.execute_query(metadata_query)
            if metadata_rows:
                table_names = [row['table_name'] for row in metadata_rows]
                self.logger.debug(f"🔧 [P1修复] 降级查询成功，从元数据找到 {len(table_names)} 个表")
                return table_names
            else:
                self.logger.debug("🔧 [P1修复] 降级查询也返回空结果")
                return []

        except Exception as e:
            self.logger.warning(f"🔧 [P1修复] 降级查询失败: {e}")
            return []

    def _check_table_metadata_exists(self, table_type: str) -> bool:
        """检查是否存在指定类型的表元数据"""
        try:
            query = """
            SELECT COUNT(*) as count FROM table_metadata
            WHERE table_name LIKE ? AND table_type = ?
            """
            result = self.db_manager.execute_query(query, (f"{table_type}_%", table_type))

            if result and len(result) > 0:
                count = result[0].get('count', 0)
                return count > 0
            return False

        except Exception as e:
            self.logger.debug(f"🔧 [P1修复] 检查表元数据失败: {e}")
            return False

    def _force_database_sync(self):
        """🔧 [深度修复] 强制数据库同步（增强版本）"""
        try:
            import time

            # 1. 执行完整的WAL checkpoint
            result = self.db_manager.execute_query("PRAGMA wal_checkpoint(FULL)")
            if result and len(result) > 0:
                checkpoint_info = result[0]
                busy = checkpoint_info.get('busy', 0)
                log_pages = checkpoint_info.get('log', 0)
                checkpointed_pages = checkpoint_info.get('checkpointed', 0)

                self.logger.debug(f"🔧 [深度修复] WAL checkpoint结果: busy={busy}, log={log_pages}, checkpointed={checkpointed_pages}")

                # 如果还有忙碌页面，等待并重试
                if busy > 0:
                    self.logger.debug("🔧 [深度修复] 检测到忙碌页面，等待后重试")
                    time.sleep(0.2)
                    # 使用TRUNCATE模式强制完成
                    self.db_manager.execute_query("PRAGMA wal_checkpoint(TRUNCATE)")

            # 2. 强制刷新数据库缓存
            self.db_manager.execute_query("PRAGMA cache_size = -2000")  # 重置缓存

            # 3. 验证同步效果
            time.sleep(0.1)  # 给数据库一点时间完成同步

            self.logger.debug("🔧 [深度修复] 强制数据库同步完成")

        except Exception as e:
            self.logger.warning(f"🔧 [深度修复] 强制数据库同步失败: {e}")

    def _get_expected_table_count(self, table_type: str) -> int:
        """[FIX] [P3修复] 获取特定表类型的预期表数量"""
        # [FIX] [P3修复] 不再强制要求表必须存在，允许空数据库状态
        # 这避免了系统初始启动时的虚假警告
        return 0  # 允许任何数量的表，包括0个

    def get_navigation_tree_data(self) -> Dict[int, Dict[int, List[Dict[str, str]]]]:
        """
        从元数据获取用于构建导航树的结构化数据

        Returns:
            Dict[int, Dict[int, List[Dict[str, str]]]]:
            一个嵌套字典，结构为 {year: {month: [item_info, ...]}}
        """
        try:
            metadata = self.get_table_list(table_type='salary_data')
            if not metadata:
                self.logger.warning("在 table_metadata 中未找到任何 'salary_data' 类型的表")
                return {}

            tree_data = {}

            # 定义专业图标映射 - P1修复：替换方括号为专业图标
            icon_map = {
                '全部在职人员': '👥',  # 团队图标，表示全体在职人员
                '离休人员': '🏆',     # 奖杯图标，表示荣誉离休
                '退休人员': '🌅',     # 日出图标，表示退休生活
                'A岗职工': '⭐'      # 星星图标，表示A岗特殊岗位
            }

            for item in metadata:
                year = item.get('year')
                month = item.get('month')

                if not year or not month:
                    self.logger.warning(f"元数据项 {item.get('table_name')} 缺少年份或月份信息，已跳过")
                    continue

                # 确保年份和月份是整数
                try:
                    year = int(year)
                    month = int(month)
                except (ValueError, TypeError):
                    self.logger.warning(f"元数据项 {item.get('table_name')} 的年份或月份格式无效，已跳过")
                    continue

                if year not in tree_data:
                    tree_data[year] = {}
                if month not in tree_data[year]:
                    tree_data[year][month] = []

                display_name = item.get('display_name', item.get('table_name'))

                tree_data[year][month].append({
                    'display_name': display_name,
                    'table_name': item.get('table_name'),
                    'icon': icon_map.get(display_name, '📄')
                })

            # 按年份和月份排序
            sorted_tree_data = {y: dict(sorted(tree_data[y].items(), key=lambda m: m[0], reverse=True))
                                for y in sorted(tree_data.keys(), reverse=True)}

            return sorted_tree_data

        except Exception as e:
            self.logger.error(f"获取导航树数据失败: {e}", exc_info=True)
            return {}

    # ================== 异动表 动态导航 ==================
    def get_change_navigation_tree_data(self) -> Dict[int, Dict[int, List[Dict[str, str]]]]:
        """
        从元数据获取用于构建异动表导航树的结构化数据

        Returns:
            {year: {month: [ {display_name, table_name, icon} ]}}
        """
        try:
            metadata = self.get_table_list(table_type='change_data')
            if not metadata:
                self.logger.warning("在 table_metadata 中未找到任何 'change_data' 类型的表")
                return {}

            tree_data: Dict[int, Dict[int, List[Dict[str, str]]]] = {}

            icon_map = {
                '起薪': '🆕',
                '转正定级': '✅',
                '停薪-退休': '⏸️',
                '停薪-调离': '🔀',
                '考核扣款': '💸',
            }

            for item in metadata:
                year = item.get('year')
                month = item.get('month')
                if not year or not month:
                    continue
                try:
                    year = int(year)
                    month = int(month)
                except (ValueError, TypeError):
                    continue

                if year not in tree_data:
                    tree_data[year] = {}
                if month not in tree_data[year]:
                    tree_data[year][month] = []

                display_name = item.get('display_name', item.get('table_name'))
                tree_data[year][month].append({
                    'display_name': display_name,
                    'table_name': item.get('table_name'),
                    'icon': icon_map.get(display_name, '📄')
                })

            sorted_tree_data = {y: dict(sorted(tree_data[y].items(), key=lambda m: m[0], reverse=True))
                                for y in sorted(tree_data.keys(), reverse=True)}
            return sorted_tree_data
        except Exception as e:
            self.logger.error(f"获取异动导航树数据失败: {e}", exc_info=True)
            return {}

    def get_latest_change_data_path(self) -> Optional[str]:
        """
        获取最新异动数据的导航路径

        Returns:
            e.g. "异动人员表 > 2025年 > 06月 > 起薪"
        """
        try:
            metadata = self.get_table_list(table_type='change_data')
            if not metadata:
                self.logger.warning("未找到任何异动数据表")
                return None

            latest_year = None
            latest_month = None
            latest_item = None

            for item in metadata:
                year = item.get('year')
                month = item.get('month')
                display_name = item.get('display_name', '')
                if not year or not month:
                    continue
                try:
                    year = int(year)
                    month = int(month)
                except (ValueError, TypeError):
                    continue

                if latest_year is None or year > latest_year or (year == latest_year and month > latest_month):
                    latest_year = year
                    latest_month = month
                    latest_item = item

            if latest_item is None:
                return None

            display_name = latest_item.get('display_name', '起薪')
            return f"异动人员表 > {latest_year}年 > {latest_month:02d}月 > {display_name}"
        except Exception as e:
            self.logger.error(f"获取最新异动数据路径失败: {e}", exc_info=True)
            return None

    def get_latest_salary_data_path(self) -> Optional[str]:
        """
        获取最新工资数据的导航路径

        Returns:
            Optional[str]: 最新数据的导航路径，格式如 "工资表 > 2025年 > 06月 > 全部在职人员"
        """
        try:
            self.logger.info("开始获取最新工资数据路径...")

            # 获取所有工资数据表
            metadata = self.get_table_list(table_type='salary_data')
            if not metadata:
                self.logger.warning("未找到任何工资数据表")
                return None

            # 按年份和月份排序，找到最新的数据
            latest_year = None
            latest_month = None
            latest_active_table = None

            for item in metadata:
                year = item.get('year')
                month = item.get('month')
                display_name = item.get('display_name', '')

                if not year or not month:
                    continue

                try:
                    year = int(year)
                    month = int(month)
                except (ValueError, TypeError):
                    continue

                # 更新最新年月
                if latest_year is None or year > latest_year or (year == latest_year and month > latest_month):
                    latest_year = year
                    latest_month = month
                    # 优先选择"全部在职人员"，如果没有则选择第一个
                    if display_name == '全部在职人员':
                        latest_active_table = item
                    elif latest_active_table is None or latest_active_table.get('display_name') != '全部在职人员':
                        latest_active_table = item
                elif year == latest_year and month == latest_month and display_name == '全部在职人员':
                    # 同年同月的情况下，优先选择"全部在职人员"
                    latest_active_table = item

            if latest_active_table is None:
                self.logger.warning("未找到有效的工资数据表")
                return None

            # 构造导航路径
            display_name = latest_active_table.get('display_name', '全部在职人员')
            path = f"工资表 > {latest_year}年 > {latest_month:02d}月 > {display_name}"

            self.logger.info(f"找到最新工资数据路径: {path}")
            return path

        except Exception as e:
            self.logger.error(f"获取最新工资数据路径失败: {e}", exc_info=True)
            return None

    def _parse_table_name(self, table_name: str) -> Optional[Tuple[int, int, str]]:
        """
        解析表名获取年月和员工类型
        
        Args:
            table_name: 表名 (例如: salary_data_2025_02_retired_employees)

        Returns:
            Optional[Tuple[int, int, str]]: 返回年份、月份和员工类型
        """
        try:
            parts = table_name.split('_')
            if len(parts) >= 5 and parts[0] == 'salary' and parts[1] == 'data':
                year = int(parts[2])
                month = int(parts[3])
                # 员工类型可能包含多个下划线
                employee_type = '_'.join(parts[4:])
                return year, month, employee_type
            else:
                return None
        except (ValueError, IndexError):
            return None

    def save_dataframe_to_table(self, df: pd.DataFrame, table_name: str) -> Tuple[bool, str]:
        """
        将DataFrame数据保存到数据库的指定表中 (覆盖写入)
        此方法是事务性的，并包含完整的错误处理。
        Returns:
            Tuple[bool, str]: A tuple containing a boolean success flag 
                              and a message string.
        """
        if df is None or df.empty:
            self.logger.warning("输入数据为空，无法保存。")
            return False, "输入数据为空，无法保存。"

        try:
            # Step 1: Prepare DataFrame
            df_copy = df.copy()
            
            # [FIX] [修复标识] 导入时字段映射：Excel列名 → 数据库字段名
            # 注意：这里df_copy.columns是Excel字段名，需要映射为数据库字段名
            display_to_db_mappings = self._get_import_field_mappings(table_name, df_copy.columns.tolist())
            
            print(f"[FIX] [修复标识] 导入字段映射加载完成: {len(display_to_db_mappings)} 个映射规则")
            self.logger.info(f"[FIX] [修复标识] 导入字段映射加载完成: {len(display_to_db_mappings)} 个映射规则")
            
            # 应用字段映射：{excel_field: db_field}
            active_map = {k: v for k, v in display_to_db_mappings.items() if k in df_copy.columns}
            
            # 对于未映射的列，保留原名
            unmapped_columns = [col for col in df_copy.columns if col not in active_map]
            if unmapped_columns:
                self.logger.info(f"[FIX] [修复标识] 保留未映射列: {unmapped_columns}")
                print(f"[FIX] [修复标识] 保留未映射列: {unmapped_columns}")
                # 为未映射列添加直接映射
                for col in unmapped_columns:
                    active_map[col] = col
            
            if active_map:
                df_copy.rename(columns=active_map, inplace=True)
                mapped_info = f"{len(active_map)} 个字段已映射"
                self.logger.info(f"[FIX] [修复标识] 导入列名映射成功: {mapped_info}")
                print(f"[FIX] [修复标识] 导入列名映射成功: {mapped_info}")
                
                # 打印映射详情（仅前10个）
                sample_mappings = dict(list(active_map.items())[:10])
                print(f"[FIX] [修复标识] 导入映射样例: {sample_mappings}")
            else:
                self.logger.info("[FIX] [修复标识] 未找到匹配的导入字段映射，保留原始列名")
                print(f"[FIX] [修复标识] 未找到匹配的导入字段映射，保留原始列名")
            
            # [FIX] [修复标识] 现在检查必须字段 - 映射完成后应该有employee_id字段
            if 'employee_id' not in df_copy.columns:
                # 提供详细的错误信息，显示当前可用的字段
                available_columns = list(df_copy.columns)
                error_msg = f"数据中缺少必须的员工ID列 (工号/职工编号/编号/人员代码)。当前可用字段: {available_columns}"
                print(f"[FIX] [修复标识] 字段验证失败: {error_msg}")
                self.logger.error(f"[FIX] [修复标识] 字段验证失败: {error_msg}")
                raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号/编号/人员代码)")

            # 1b. Clean all other column names
            final_column_map = {col: self._clean_column_name(col) for col in df_copy.columns}
            df_copy.rename(columns=final_column_map, inplace=True)
            prepared_df = df_copy

            # Step 2: Ensure table exists
            if not self.db_manager.table_exists(table_name):
                self.logger.info(f"表 {table_name} 不存在，将根据模板创建...")
                
                # 改进的模板检测逻辑
                template_key = None
                if "salary_data" in table_name:
                    # 所有salary_data相关的表都使用salary_data模板
                    template_key = "salary_data"
                elif "misc_data" in table_name:
                    template_key = "misc_data"
                elif "salary_changes" in table_name:
                    template_key = "salary_changes"
                else:
                    # 如果表名不匹配任何已知模板，尝试根据表名前缀推断
                    table_prefix = table_name.split('_')[0]
                    if table_prefix in self._table_templates:
                        template_key = table_prefix
                    else:
                        # 默认使用salary_data模板作为兜底
                        template_key = "salary_data"
                        self.logger.warning(f"表 {table_name} 无法匹配具体模板，使用默认salary_data模板")
                
                if not template_key:
                    raise ValueError(f"无法为表 {table_name} 找到合适的创建模板。")
                
                base_schema = self._table_templates[template_key]
                # 创建基本表结构，不需要动态扩展
                final_schema = TableSchema(
                    table_name=table_name,
                    columns=base_schema.columns,
                    primary_key=base_schema.primary_key,
                    indexes=base_schema.indexes,
                    description=f"动态创建的表: {table_name}"
                )
                
                if not self._create_table_from_schema(final_schema):
                    raise RuntimeError(f"创建表 {table_name} 失败。")

            # Step 3: Insert data
            table_info = self.db_manager.get_table_info(table_name)
            final_columns = {col['name'] for col in table_info}
            
            # 自动填充必须字段
            current_time = datetime.now().isoformat()
            
            # 从表名中提取年月信息（格式：salary_data_2025_05_xxxx）
            table_parts = table_name.split('_')
            if len(table_parts) >= 4 and table_parts[0] == 'salary' and table_parts[1] == 'data':
                try:
                    year_from_table = int(table_parts[2])
                    month_from_table = table_parts[3]
                    # 格式化月份为 YYYY-MM 格式
                    if len(month_from_table) == 2:
                        month_str = f"{year_from_table}-{month_from_table}"
                    else:
                        month_str = f"{year_from_table}-{month_from_table.zfill(2)}"
                except (ValueError, IndexError):
                    # 如果无法从表名解析，使用当前时间
                    now = datetime.now()
                    year_from_table = now.year
                    month_str = now.strftime("%Y-%m")
            else:
                # 非标准表名，使用当前时间
                now = datetime.now()
                year_from_table = now.year
                month_str = now.strftime("%Y-%m")
            
            # [FIX] [修复标识] 添加必须字段的默认值 - 修复离休人员字段None问题
            for col_name in final_columns:
                if col_name not in prepared_df.columns:
                    if col_name == 'month':
                        prepared_df[col_name] = month_str
                    elif col_name == 'year':
                        prepared_df[col_name] = year_from_table
                    elif col_name == 'created_at':
                        prepared_df[col_name] = current_time
                    elif col_name == 'updated_at':
                        prepared_df[col_name] = current_time
                    elif col_name == 'id':
                        # id 是自增字段，不需要手动设置
                        continue
                    # [FIX] [修复标识] 对于薪资数值字段，设置为0.0而不是None
                    elif col_name in ['basic_retirement_salary', 'balance_allowance', 'living_allowance', 
                                    'housing_allowance', 'property_allowance', 'retirement_allowance',
                                    'one_time_living_allowance', 'supplement', 'advance', 'nursing_fee',
                                    'total', 'allowance', 'basic_salary', 'total_salary']:
                        prepared_df[col_name] = 0.0
                        print(f"[FIX] [修复标识] 数值字段 {col_name} 设置默认值 0.0")
                        self.logger.info(f"[FIX] [修复标识] 数值字段 {col_name} 设置默认值 0.0")
                    else:
                        # 对于其他字段，设置默认值
                        if col_name in ['employee_name', 'department', 'remarks']:
                            prepared_df[col_name] = ''  # 文本字段设置为空字符串
                        else:
                            prepared_df[col_name] = None
                        print(f"[FIX] [修复标识] 字段 {col_name} 设置默认值")
                        self.logger.info(f"[FIX] [修复标识] 字段 {col_name} 设置默认值")
            
            df_to_insert = prepared_df[[col for col in prepared_df.columns if col in final_columns]]
            
            # 清空表
            self.db_manager.execute_update(f"DELETE FROM {self._quote_identifier(table_name)}")

            # 批量插入数据
            columns = df_to_insert.columns.tolist()
            quoted_columns = [self._quote_identifier(col) for col in columns]
            placeholders = ', '.join(['?'] * len(columns))
            sql = f"INSERT INTO {self._quote_identifier(table_name)} ({', '.join(quoted_columns)}) VALUES ({placeholders})"
            
            data = [tuple(row) for row in df_to_insert.itertuples(index=False, name=None)]
            self.db_manager.execute_batch(sql, data)
            
            self.logger.info(f"成功向表 {table_name} 保存 {len(data)} 条数据。")
            return True, f"成功向表 {table_name} 保存 {len(data)} 条数据。"
            
        except Exception as e:
            self.logger.error(f"保存数据到表 {table_name} 失败: {e}", exc_info=True)
            return False, f"保存数据到表 {table_name} 失败: {e}"

    def _get_import_field_mappings(self, table_name: str, excel_columns: List[str]) -> Dict[str, str]:
        """
        [FIX] [修复标识] 获取导入时的字段映射：Excel列名 → 数据库字段名 - 增强版
        
        Args:
            table_name: 数据库表名
            excel_columns: Excel实际列名列表
            
        Returns:
            Dict[str, str]: 导入字段映射字典 {excel_column: db_field}
        """
        try:
            print(f"[FIX] [修复标识] 开始获取导入字段映射: {table_name}")
            print(f"[FIX] [修复标识] 输入Excel列名数量: {len(excel_columns)}")
            print(f"[FIX] [修复标识] Excel列名: {excel_columns}")
            
            # 首先尝试获取动态映射
            display_mappings = self._get_dynamic_field_mappings(table_name, excel_columns)
            
            # 创建反向映射 {display_name: db_field}
            import_mappings = {}
            if display_mappings:
                for db_field, display_name in display_mappings.items():
                    import_mappings[display_name] = db_field
                print(f"[FIX] [修复标识] 从动态映射获得: {len(import_mappings)} 个规则")
            else:
                print(f"[FIX] [修复标识] 动态映射为空，使用兜底映射")
            
            # [FIX] [修复标识] 无论如何都要应用兜底映射，确保重要字段不遗漏
            fallback_mappings = self._get_fallback_import_mappings()
            
            # 合并映射，优先使用动态映射，兜底映射作为补充
            for excel_col in excel_columns:
                if excel_col not in import_mappings:
                    if excel_col in fallback_mappings:
                        import_mappings[excel_col] = fallback_mappings[excel_col]
                        print(f"[FIX] [修复标识] 兜底映射: {excel_col} -> {fallback_mappings[excel_col]}")
            
            # 特殊处理员工ID字段的多种表示方式
            employee_id_variants = ['工号', '职工编号', '编号', '人员代码']
            for variant in employee_id_variants:
                if variant in excel_columns:
                    import_mappings[variant] = 'employee_id'
                    print(f"[FIX] [修复标识] 员工ID字段映射: {variant} -> employee_id")
            
            print(f"[FIX] [修复标识] 最终导入映射: {len(import_mappings)} 个规则")
            
            # 检查离休人员关键字段的映射情况
            if "retired_employees" in table_name:
                key_fields = ["基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "增发一次性生活补贴"]
                print(f"[FIX] [修复标识] 离休人员关键字段映射检查:")
                for field in key_fields:
                    if field in import_mappings:
                        print(f"  ✅ {field} -> {import_mappings[field]}")
                    elif field in excel_columns:
                        print(f"  ❌ {field} 存在于Excel但无映射")
                    else:
                        print(f"  ⚠️ {field} 不在Excel列中")
            
            # 打印映射样例
            sample_import_mappings = dict(list(import_mappings.items())[:15])
            print(f"[FIX] [修复标识] 导入映射样例: {sample_import_mappings}")
            
            return import_mappings
            
        except Exception as e:
            self.logger.error(f"生成导入字段映射失败: {e}")
            import traceback
            traceback.print_exc()
            print(f"[FIX] [修复标识] 映射失败，使用完整兜底映射")
            # 返回完整的兜底映射
            return self._get_fallback_import_mappings()
    
    def _get_fallback_import_mappings(self) -> Dict[str, str]:
        """[FIX] [修复标识] 获取兜底导入字段映射 - 增强离休人员字段支持"""
        # 基础映射
        mappings = {
            # 员工ID的多种表示方式
            "工号": "employee_id",
            "职工编号": "employee_id", 
            "编号": "employee_id",
            "人员代码": "employee_id",
            # 基础字段
            "姓名": "employee_name",
            "部门名称": "department", 
            "津贴": "allowance",
            "应发工资": "total_salary",
            "基本工资": "basic_salary",
            "序号": "sequence_number",
            "护理费": "nursing_fee",
            "合计": "total",
            "补发": "supplement",
            "借支": "advance", 
            "备注": "remarks",
            # 离休人员特有字段 - 标准格式
            "基本离休费": "basic_retirement_salary",
            "结余津贴": "balance_allowance",
            "生活补贴": "living_allowance",
            "住房补贴": "housing_allowance", 
            "物业补贴": "property_allowance",
            "离休补贴": "retirement_allowance",
            "增发一次性生活补贴": "one_time_living_allowance",
            # 离休人员特有字段 - 带换行符格式
            "基本\n离休费": "basic_retirement_salary",
            "结余\n津贴": "balance_allowance",
            "生活\n补贴": "living_allowance",
            "住房\n补贴": "housing_allowance",
            "物业\n补贴": "property_allowance",
            "离休\n补贴": "retirement_allowance",
            "增发一次\n性生活补贴": "one_time_living_allowance"
        }
        
        # [FIX] [修复标识] 增强模糊匹配：自动处理空格、制表符、换行符等
        enhanced_mappings = {}
        for excel_name, db_field in mappings.items():
            enhanced_mappings[excel_name] = db_field
            
            # 添加去除空白字符的版本
            clean_name = excel_name.replace('\n', '').replace('\t', '').replace(' ', '')
            if clean_name != excel_name:
                enhanced_mappings[clean_name] = db_field
            
            # 添加只包含换行符替换为空格的版本
            space_name = excel_name.replace('\n', ' ').replace('\t', ' ')
            if space_name != excel_name:
                enhanced_mappings[space_name] = db_field
        
        return enhanced_mappings

    def _get_dynamic_field_mappings(self, table_name: str, excel_columns: List[str]) -> Dict[str, str]:
        """
        [FIX] [修复标识] 根据表名和Excel列名动态获取字段映射 - 修复版
        
        Args:
            table_name: 数据库表名
            excel_columns: 数据库实际列名列表（注意：这里是数据库列名，不是Excel列名）
            
        Returns:
            Dict[str, str]: 字段映射字典 {db_column: display_name}
        """
        try:
            print(f"[FIX] [修复标识] 开始获取字段映射: {table_name}")
            print(f"[FIX] [修复标识] 输入列名数量: {len(excel_columns)}")
            
            # 加载字段映射配置文件
            mapping_file = Path("state/data/field_mappings.json")
            if not mapping_file.exists():
                self.logger.warning("字段映射配置文件不存在，使用基础映射")
                return self._get_fallback_field_mappings_corrected()
            
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping_config = json.load(f)
            
            # 方法1: 尝试直接匹配表名
            if table_name in mapping_config.get("table_mappings", {}):
                table_config = mapping_config["table_mappings"][table_name]
                field_mappings = table_config.get("field_mappings", {})
                print(f"[FIX] [修复标识] 找到表级映射: {len(field_mappings)} 个字段")
                # 保持原始映射: {db_field: display_name}
                return field_mappings
            
            # 方法2: 根据表名模式推断模板类型
            template_type = self._infer_template_type_from_table_name(table_name)
            if template_type and template_type in mapping_config.get("field_templates", {}):
                template_mappings = mapping_config["field_templates"][template_type]
                print(f"[FIX] [修复标识] 使用模板映射: {template_type}, {len(template_mappings)} 个字段")
                # 保持原始映射: {db_field: display_name}
                return template_mappings
            
            # 方法3: 查找最相似的现有表配置
            best_match = self._find_best_matching_table_config_corrected(table_name, excel_columns, mapping_config)
            if best_match:
                print(f"[FIX] [修复标识] 使用最佳匹配映射: {len(best_match)} 个字段")
                return best_match
            
            # 兜底: 使用基础映射
            fallback_mappings = self._get_fallback_field_mappings_corrected()
            print(f"[FIX] [修复标识] 使用基础映射: {len(fallback_mappings)} 个字段")
            self.logger.warning(f"未找到表 {table_name} 的专用映射，使用基础映射")
            return fallback_mappings
            
        except Exception as e:
            self.logger.error(f"加载动态字段映射失败: {e}")
            import traceback
            traceback.print_exc()
            return self._get_fallback_field_mappings_corrected()
    
    def _infer_template_type_from_table_name(self, table_name: str) -> Optional[str]:
        """从表名推断模板类型"""
        if "retired_employees" in table_name:
            return "离休人员工资表"
        elif "pension_employees" in table_name:
            return "退休人员工资表"
        elif "active_employees" in table_name:
            return "全部在职人员工资表"
        elif "a_grade_employees" in table_name:
            return "A岗职工"
        return None
    
    def _find_best_matching_table_config_corrected(self, table_name: str, db_columns: List[str], mapping_config: Dict) -> Optional[Dict[str, str]]:
        """[FIX] [修复标识] 查找最匹配的现有表配置 - 强化跨月份映射版"""
        table_mappings = mapping_config.get("table_mappings", {})
        best_match = None
        best_score = 0
        
        # 提取当前表的员工类型，用于智能匹配
        current_employee_type = self._extract_employee_type(table_name)
        print(f"[FIX] [修复标识] 当前表员工类型: {current_employee_type}")
        
        for existing_table_name, config in table_mappings.items():
            existing_employee_type = self._extract_employee_type(existing_table_name)
            print(f"[FIX] [修复标识] 比较表: {existing_table_name}, 员工类型: {existing_employee_type}")
            
            if existing_table_name == table_name:
                print(f"[FIX] [修复标识] 跳过相同表名: {existing_table_name}")
                continue
                
            # [FIX] [修复标识] 强化表名相似度检查（优先匹配相同员工类型）
            if current_employee_type and existing_employee_type and current_employee_type == existing_employee_type:
                field_mappings = config.get("field_mappings", {})
                db_fields = set(field_mappings.keys())
                db_columns_set = set(db_columns)
                
                # 计算匹配度
                match_count = len(db_fields.intersection(db_columns_set))
                total_count = len(db_columns_set)
                score = match_count / total_count if total_count > 0 else 0
                
                print(f"[FIX] [修复标识] 匹配评分: {existing_table_name} = {score:.2f} ({match_count}/{total_count})")
                
                if score > best_score and score > 0.5:  # 至少50%匹配度
                    best_match = field_mappings
                    best_score = score
                    print(f"[FIX] [修复标识] 新的最佳匹配: {existing_table_name}, 得分: {score:.2f}")
        
        if best_match:
            print(f"[FIX] [修复标识] 找到跨月份最佳匹配，匹配度: {best_score:.2f}")
            self.logger.info(f"找到最佳匹配配置，匹配度: {best_score:.2%}")
            return best_match
        
        print(f"[FIX] [修复标识] 未找到合适的跨月份匹配")
        return None
    
    def _extract_employee_type(self, table_name: str) -> str:
        """从表名提取员工类型"""
        if "retired_employees" in table_name:
            return "retired_employees"
        elif "pension_employees" in table_name:
            return "pension_employees"
        elif "active_employees" in table_name:
            return "active_employees"
        elif "a_grade_employees" in table_name:
            return "a_grade_employees"
        return "unknown"
    
    def _get_template_field_mapping(self, template_type: str) -> Optional[Dict[str, str]]:
        """[FIX] [修复标识] 从字段模板获取映射配置"""
        try:
            mapping_file = Path("state/data/field_mappings.json")
            if not mapping_file.exists():
                return None
            
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping_config = json.load(f)
            
            field_templates = mapping_config.get("field_templates", {})
            if template_type in field_templates:
                template_mapping = field_templates[template_type]
                print(f"[FIX] [修复标识] 从模板 {template_type} 获得 {len(template_mapping)} 个字段映射")
                return template_mapping
            
            return None
        except Exception as e:
            self.logger.error(f"获取模板字段映射失败: {e}")
            return None
    
    def _get_fallback_field_mappings_corrected(self) -> Dict[str, str]:
        """[FIX] [修复标识] 获取兜底字段映射 - 修正版"""
        return {
            # 基础字段映射 {db_field: display_name}
            "employee_id": "工号",
            "employee_name": "姓名", 
            "department": "部门名称",
            "allowance": "津贴",
            "total_salary": "应发工资",
            "basic_salary": "基本工资",
            "sequence_number": "序号",
            "nursing_fee": "护理费",
            "total": "合计",
            "supplement": "补发",
            "advance": "借支",
            "remarks": "备注",
            # 离休人员特有字段
            "basic_retirement_salary": "基本离休费",
            "balance_allowance": "结余津贴",
            "living_allowance": "生活补贴",
            "housing_allowance": "住房补贴",
            "property_allowance": "物业补贴",
            "retirement_allowance": "离休补贴",
            "one_time_living_allowance": "增发一次性生活补贴"
        }

    @staticmethod
    def _clean_column_name(col_name: str) -> str:
        """清理列名，使其符合SQL规范。"""
        if not col_name:
            return "unknown_column"
        
        # 保持原始列名，只进行必要的清理
        # 移除前后空格
        cleaned = str(col_name).strip()
        
        # 如果是空字符串，返回默认名称
        if not cleaned:
            return "unknown_column"
        
        return cleaned
